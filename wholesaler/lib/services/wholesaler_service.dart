import 'package:get/get.dart';
import 'package:tagerplus/core/api/client/gomla_service.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';

class WholesalerService extends GetxService {
  static WholesalerService get to => Get.find();

  late final GomlaService _gomlaService;

  // Observable data
  final _dashboard = Rx<DashboardOut?>(null);
  final _orders = <OrderDetailOut>[].obs;
  final _items = <ItemOut>[].obs;
  final _isLoading = false.obs;
  final _error = Rx<String?>(null);

  // Getters
  DashboardOut? get dashboard => _dashboard.value;
  List<OrderDetailOut> get orders => _orders;
  List<ItemOut> get items => _items;
  bool get isLoading => _isLoading.value;
  String? get error => _error.value;

  @override
  void onInit() {
    super.onInit();
    _gomlaService = ApiService.to.apiClient.gomla;
  }

  // Dashboard methods
  Future<void> loadDashboard() async {
    try {
      _isLoading.value = true;
      _error.value = null;

      final response = await _gomlaService.getDashboard();
      _dashboard.value = response.data;
    } catch (e) {
      _error.value = 'فشل في تحميل البيانات: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  // Orders methods
  Future<void> loadOrders({
    int page = 1,
    int pageSize = 20,
    String? status,
    String? search,
    String? dateFrom,
    String? dateTo,
  }) async {
    try {
      _isLoading.value = true;
      _error.value = null;

      final response = await _gomlaService.listOrders(
        page: page,
        pageSize: pageSize,
        status: status,
        search: search,
        dateFrom: dateFrom,
        dateTo: dateTo,
      );

      if (page == 1) {
        _orders.clear();
      }
      _orders.addAll(response.data?.orders ?? []);
    } catch (e) {
      _error.value = 'فشل في تحميل الطلبات: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  Future<OrderDetailOut?> getOrderDetail(int orderId) async {
    try {
      _isLoading.value = true;
      _error.value = null;

      final response = await _gomlaService.getOrderDetail(orderId);
      return response.data;
    } catch (e) {
      _error.value = 'فشل في تحميل تفاصيل الطلب: ${e.toString()}';
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> updateOrderStatus(int orderId, String status) async {
    try {
      _isLoading.value = true;
      _error.value = null;

      await _gomlaService.updateOrderStatus(
        orderId,
        UpdateOrderStatusIn(status: status),
      );

      // Refresh orders list
      await loadOrders();
    } catch (e) {
      _error.value = 'فشل في تحديث حالة الطلب: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  // Items methods
  Future<void> loadItems({
    int page = 1,
    int pageSize = 20,
    String? search,
    int? company,
    int? category,
    String? stock,
    String? sort,
  }) async {
    try {
      _isLoading.value = true;
      _error.value = null;

      final response = await _gomlaService.listItems(
        page: page,
        pageSize: pageSize,
        search: search,
        company: company,
        category: category,
        stock: stock,
        sort: sort,
      );

      if (page == 1) {
        _items.clear();
      }
      _items.addAll(response.data?.items ?? []);
    } catch (e) {
      _error.value = 'فشل في تحميل المنتجات: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  Future<ItemOut?> getItemDetail(int itemId) async {
    try {
      _isLoading.value = true;
      _error.value = null;

      final response = await _gomlaService.getItemDetail(itemId);
      return response.data;
    } catch (e) {
      _error.value = 'فشل في تحميل تفاصيل المنتج: ${e.toString()}';
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> updateItem(int itemId, ItemUpdateIn updateData) async {
    try {
      _isLoading.value = true;
      _error.value = null;

      await _gomlaService.updateItem(itemId, updateData);

      // Refresh items list
      await loadItems();
    } catch (e) {
      _error.value = 'فشل في تحديث المنتج: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  // Utility methods
  void clearError() {
    _error.value = null;
  }

  void clearData() {
    _dashboard.value = null;
    _orders.clear();
    _items.clear();
    _error.value = null;
  }
}
