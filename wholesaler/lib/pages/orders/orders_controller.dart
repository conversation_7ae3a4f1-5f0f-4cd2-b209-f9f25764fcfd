import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:wholesaler/pages/orders/order_details_page.dart';
import '../../services/wholesaler_service.dart';

class OrdersController extends GetxController {
  final WholesalerService _wholesalerService = Get.put(WholesalerService());

  // Filter states
  final _selectedFilter = 'all'.obs;
  final _searchQuery = ''.obs;

  // Pagination
  final _currentPage = 1.obs;
  final _hasMore = true.obs;
  final _isLoadingMore = false.obs;

  // Getters
  String get selectedFilter => _selectedFilter.value;
  String get searchQuery => _searchQuery.value;
  List<OrderDetailOut> get orders => _wholesalerService.orders;
  bool get isLoading => _wholesalerService.isLoading;
  bool get isLoadingMore => _isLoadingMore.value;
  bool get hasMore => _hasMore.value;
  String? get error => _wholesalerService.error;

  // Filter options
  final filterOptions = [
    {'key': 'all', 'label': 'الكل'},
    {'key': 'pending', 'label': 'قيد الانتظار'},
    {'key': 'confirmed', 'label': 'مؤكد'},
    {'key': 'preparing', 'label': 'قيد التحضير'},
    {'key': 'ready', 'label': 'جاهز'},
    {'key': 'delivered', 'label': 'تم التسليم'},
    {'key': 'cancelled', 'label': 'ملغي'},
  ];

  @override
  void onInit() {
    super.onInit();
    loadOrders();
  }

  // Filter methods
  void setFilter(String filter) {
    if (_selectedFilter.value != filter) {
      _selectedFilter.value = filter;
      _currentPage.value = 1;
      _hasMore.value = true;
      loadOrders();
    }
  }

  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _currentPage.value = 1;
    _hasMore.value = true;
    loadOrders();
  }

  // Data loading methods
  Future<void> loadOrders() async {
    _currentPage.value = 1;
    await _wholesalerService.loadOrders(
      page: _currentPage.value,
      status: _selectedFilter.value == 'all' ? null : _selectedFilter.value,
      search: _searchQuery.value.isEmpty ? null : _searchQuery.value,
    );

    // Update pagination state based on response
    _hasMore.value = orders.length >= 20; // Assuming page size is 20
  }

  Future<void> loadMoreOrders() async {
    if (_isLoadingMore.value || !_hasMore.value) return;

    _isLoadingMore.value = true;
    _currentPage.value++;

    try {
      final initialCount = orders.length;
      await _wholesalerService.loadOrders(
        page: _currentPage.value,
        status: _selectedFilter.value == 'all' ? null : _selectedFilter.value,
        search: _searchQuery.value.isEmpty ? null : _searchQuery.value,
      );

      // Check if we got new items
      final newItemsCount = orders.length - initialCount;
      _hasMore.value = newItemsCount >= 20; // Assuming page size is 20
    } finally {
      _isLoadingMore.value = false;
    }
  }

  Future<void> refreshOrders() async {
    _wholesalerService.clearError();
    await loadOrders();
  }

  // Order actions
  Future<void> updateOrderStatus(int orderId, String newStatus) async {
    await _wholesalerService.updateOrderStatus(orderId, newStatus);

    if (_wholesalerService.error == null) {
      Get.snackbar(
        'تم التحديث',
        'تم تحديث حالة الطلب بنجاح',
        snackPosition: SnackPosition.bottom,
      );
    } else {
      Get.snackbar(
        'خطأ',
        _wholesalerService.error!,
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  void viewOrderDetails(int orderId) {
    Get.to(() => OrderDetailsPage(), arguments: orderId);
  }

  // Utility methods
  String getFilterLabel(String key) {
    final option = filterOptions.firstWhere(
      (option) => option['key'] == key,
      orElse: () => {'label': 'غير معروف'},
    );
    return option['label'] as String;
  }

  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'preparing':
        return Colors.purple;
      case 'ready':
        return Colors.green;
      case 'delivered':
        return Colors.teal;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String getStatusLabel(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'قيد الانتظار';
      case 'confirmed':
        return 'مؤكد';
      case 'preparing':
        return 'قيد التحضير';
      case 'ready':
        return 'جاهز';
      case 'delivered':
        return 'تم التسليم';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }
}
