import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'orders_controller.dart';
import 'widgets/order_card.dart';
import 'widgets/order_filter_tabs.dart';

class OrdersTab extends StatelessWidget {
  const OrdersTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put<OrdersController>(OrdersController());

    return Scaffold(
      backgroundColor: AppColors.scaffoldWithBoxBackground,
      body: Column(
        children: [
          // Filter tabs
          Container(
            color: Colors.white,
            child: const OrderFilterTabs(),
          ),
          
          // Orders list
          Expanded(
            child: Obx(() {
              if (controller.isLoading && controller.orders.isEmpty) {
                return const Center(
                  child: CircularProgressIndicator(
                    color: AppColors.primary,
                  ),
                );
              }

              if (controller.error != null) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        controller.error!,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: controller.refreshOrders,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                );
              }

              if (controller.orders.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.receipt_long_outlined,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد طلبات',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'لم يتم العثور على أي طلبات بالفلتر المحدد',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[500],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: controller.refreshOrders,
                color: AppColors.primary,
                child: ListView.builder(
                  padding: const EdgeInsets.all(AppDefaults.padding),
                  itemCount: controller.orders.length + (controller.hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == controller.orders.length) {
                      // Load more indicator
                      if (controller.isLoadingMore) {
                        return const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(
                            child: CircularProgressIndicator(
                              color: AppColors.primary,
                            ),
                          ),
                        );
                      } else {
                        // Load more button
                        return Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Center(
                            child: ElevatedButton(
                              onPressed: controller.loadMoreOrders,
                              child: const Text('تحميل المزيد'),
                            ),
                          ),
                        );
                      }
                    }

                    final order = controller.orders[index];
                    return OrderCard(
                      order: order,
                      onTap: () => controller.viewOrderDetails(order.id),
                      onStatusUpdate: (newStatus) => 
                          controller.updateOrderStatus(order.id, newStatus),
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}
