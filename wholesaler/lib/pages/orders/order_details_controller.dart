import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:pdf/pdf.dart';
// import 'package:pdf/widgets.dart' as pw;
// import 'package:printing/printing.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../services/wholesaler_service.dart';

class OrderDetailsController extends GetxController {
  final WholesalerService _wholesalerService = Get.find<WholesalerService>();

  // Order ID parameter
  late final int orderId;

  // State
  final _orderDetails = Rx<OrderDetailOut?>(null);
  final _isLoading = false.obs;
  final _isUpdatingStatus = false.obs;
  final _error = Rx<String?>(null);

  // Getters
  OrderDetailOut? get orderDetails => _orderDetails.value;
  bool get isLoading => _isLoading.value;
  bool get isUpdatingStatus => _isUpdatingStatus.value;
  String? get error => _error.value;

  // Status options for updates
  final statusOptions = [
    {'key': 'confirmed', 'label': 'تأكيد الطلب', 'icon': Icons.check_circle},
    {'key': 'preparing', 'label': 'بدء التحضير', 'icon': Icons.kitchen},
    {'key': 'ready', 'label': 'جاهز للتسليم', 'icon': Icons.done_all},
    {'key': 'delivered', 'label': 'تم التسليم', 'icon': Icons.local_shipping},
    {'key': 'cancelled', 'label': 'إلغاء الطلب', 'icon': Icons.cancel},
  ];

  @override
  void onInit() {
    super.onInit();
    orderId = Get.arguments as int;
    loadOrderDetails();
  }

  // Data loading methods
  Future<void> loadOrderDetails() async {
    try {
      _isLoading.value = true;
      _error.value = null;

      final orderDetail = await _wholesalerService.getOrderDetail(orderId);
      if (orderDetail != null) {
        _orderDetails.value = orderDetail;
      } else {
        _error.value = 'لم يتم العثور على تفاصيل الطلب';
      }
    } catch (e) {
      _error.value = 'فشل في تحميل تفاصيل الطلب: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> refreshOrderDetails() async {
    _error.value = null;
    await loadOrderDetails();
  }

  // Order status management
  Future<void> updateOrderStatus(String newStatus) async {
    if (_orderDetails.value == null) return;

    try {
      _isUpdatingStatus.value = true;
      _error.value = null;

      await _wholesalerService.updateOrderStatus(orderId, newStatus);

      if (_wholesalerService.error == null) {
        // Refresh order details to get updated status
        await loadOrderDetails();

        Get.snackbar(
          'تم التحديث',
          'تم تحديث حالة الطلب إلى ${getStatusLabel(newStatus)}',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        _error.value = _wholesalerService.error;
        Get.snackbar(
          'خطأ',
          _wholesalerService.error!,
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      _error.value = 'فشل في تحديث حالة الطلب: ${e.toString()}';
      Get.snackbar(
        'خطأ',
        _error.value!,
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isUpdatingStatus.value = false;
    }
  }

  // Status update confirmation dialog
  void showStatusUpdateDialog() {
    if (_orderDetails.value == null) return;

    final currentStatus = _orderDetails.value!.status.toLowerCase();
    final availableOptions = _getAvailableStatusOptions(currentStatus);

    if (availableOptions.isEmpty) {
      Get.snackbar(
        'تنبيه',
        'لا توجد خيارات متاحة لتحديث حالة هذا الطلب',
        snackPosition: SnackPosition.bottom,
      );
      return;
    }

    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تحديث حالة الطلب',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...availableOptions.map((option) {
              return ListTile(
                leading: Icon(
                  option['icon'] as IconData,
                  color: getStatusColor(option['key'] as String),
                ),
                title: Text(option['label'] as String),
                onTap: () {
                  Get.back();
                  _confirmStatusUpdate(
                    option['key'] as String,
                    option['label'] as String,
                  );
                },
              );
            }),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Get.back(),
                child: const Text('إلغاء'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _confirmStatusUpdate(String newStatus, String statusLabel) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد التحديث'),
        content: Text('هل أنت متأكد من تحديث حالة الطلب إلى "$statusLabel"؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Get.back();
              updateOrderStatus(newStatus);
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getAvailableStatusOptions(String currentStatus) {
    switch (currentStatus) {
      case 'pending':
        return statusOptions
            .where(
              (option) => ['confirmed', 'cancelled'].contains(option['key']),
            )
            .toList();
      case 'confirmed':
        return statusOptions
            .where(
              (option) => ['preparing', 'cancelled'].contains(option['key']),
            )
            .toList();
      case 'preparing':
        return statusOptions
            .where((option) => ['ready', 'cancelled'].contains(option['key']))
            .toList();
      case 'ready':
        return statusOptions
            .where((option) => ['delivered'].contains(option['key']))
            .toList();
      default:
        return [];
    }
  }

  // Utility methods
  String getStatusLabel(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'قيد الانتظار';
      case 'confirmed':
        return 'مؤكد';
      case 'preparing':
        return 'قيد التحضير';
      case 'ready':
        return 'جاهز';
      case 'delivered':
        return 'تم التسليم';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'preparing':
        return Colors.purple;
      case 'ready':
        return Colors.green;
      case 'delivered':
        return Colors.teal;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.schedule;
      case 'confirmed':
        return Icons.check_circle;
      case 'preparing':
        return Icons.kitchen;
      case 'ready':
        return Icons.done_all;
      case 'delivered':
        return Icons.local_shipping;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year} - ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateString;
    }
  }

  double calculateSubtotal() {
    if (_orderDetails.value?.orderItems == null) return 0.0;
    return _orderDetails.value!.orderItems.fold(
      0.0,
      (sum, item) => sum + (item.pricePerUnit * item.quantity),
    );
  }

  // Order Status Management Actions
  Future<void> acceptOrder() async {
    await updateOrderStatus('processing');
  }

  Future<void> shipOrder() async {
    await updateOrderStatus('shipped');
  }

  Future<void> deliverOrder() async {
    await updateOrderStatus('delivered');
  }

  Future<void> cancelOrder() async {
    _showCancelOrderDialog();
  }

  Future<void> returnToPending() async {
    await updateOrderStatus('pending');
  }

  Future<void> returnToProcessing() async {
    await updateOrderStatus('processing');
  }

  void _showCancelOrderDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('إلغاء الطلب'),
        content: const Text(
          'هل أنت متأكد من إلغاء هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Get.back();
              updateOrderStatus('cancelled');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text(
              'تأكيد الإلغاء',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  // Item-Level Management Actions
  Future<void> removeItemFromOrder(int itemId, String productName) async {
    Get.dialog(
      AlertDialog(
        title: const Text('إزالة منتج من الطلب'),
        content: Text('هل أنت متأكد من إزالة "$productName" من الطلب؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _performRemoveItem(itemId);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إزالة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _performRemoveItem(int itemId) async {
    try {
      _isUpdatingStatus.value = true;
      _error.value = null;

      // TODO: Implement API call to remove item from order
      // await _wholesalerService.removeOrderItem(orderId, itemId);

      // For now, show success message and refresh order
      await refreshOrderDetails();

      Get.snackbar(
        'تم الحذف',
        'تم إزالة المنتج من الطلب بنجاح',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      _error.value = 'فشل في إزالة المنتج: ${e.toString()}';
      Get.snackbar(
        'خطأ',
        _error.value!,
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isUpdatingStatus.value = false;
    }
  }

  void decreaseItemQuantity(
    int itemId,
    String productName,
    int currentQuantity,
  ) {
    final controller = TextEditingController(text: currentQuantity.toString());

    Get.dialog(
      AlertDialog(
        title: Text('تعديل كمية $productName'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('الكمية الحالية: $currentQuantity'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'الكمية الجديدة',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              final newQuantity = int.tryParse(controller.text);
              if (newQuantity != null &&
                  newQuantity > 0 &&
                  newQuantity != currentQuantity) {
                Get.back();
                _performUpdateQuantity(itemId, newQuantity);
              } else {
                Get.snackbar(
                  'خطأ',
                  'يرجى إدخال كمية صحيحة',
                  snackPosition: SnackPosition.bottom,
                );
              }
            },
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  Future<void> _performUpdateQuantity(int itemId, int newQuantity) async {
    try {
      _isUpdatingStatus.value = true;
      _error.value = null;

      // TODO: Implement API call to update item quantity
      // await _wholesalerService.updateOrderItemQuantity(orderId, itemId, newQuantity);

      // For now, show success message and refresh order
      await refreshOrderDetails();

      Get.snackbar(
        'تم التحديث',
        'تم تحديث كمية المنتج بنجاح',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      _error.value = 'فشل في تحديث الكمية: ${e.toString()}';
      Get.snackbar(
        'خطأ',
        _error.value!,
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isUpdatingStatus.value = false;
    }
  }

  // Communication Actions
  Future<void> callStore() async {
    if (_orderDetails.value?.store == null) return;

    final phoneNumber = _orderDetails.value!.store.ownerPhone;
    if (phoneNumber.isEmpty) {
      Get.snackbar(
        'خطأ',
        'رقم الهاتف غير متوفر',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      final uri = Uri.parse('tel:$phoneNumber');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        Get.snackbar(
          'خطأ',
          'لا يمكن إجراء المكالمة من هذا الجهاز',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في إجراء المكالمة: ${e.toString()}',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> sendWhatsApp() async {
    if (_orderDetails.value?.store == null) return;

    final store = _orderDetails.value!.store;
    final order = _orderDetails.value!;

    // Format phone number for WhatsApp
    String phoneNumber = store.ownerPhone.replaceAll(RegExp(r'[^\d]'), '');
    if (!phoneNumber.startsWith('20') && phoneNumber.startsWith('01')) {
      phoneNumber = '2$phoneNumber'; // Add Egypt country code
    }

    // Create WhatsApp message in Arabic
    final message =
        '''
السلام عليكم ورحمة الله وبركاته

بخصوص طلب رقم: ${order.id}
تاريخ الطلب: ${formatDate(order.createdAt)}
حالة الطلب: ${getStatusLabel(order.status)}

عدد المنتجات: ${order.productsTotalQuantity}
إجمالي المبلغ: ${order.productsTotalPrice.toStringAsFixed(2)} جنيه

نتطلع للتعامل معكم
شكراً لكم
''';

    try {
      final whatsappUrl =
          'https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}';
      final uri = Uri.parse(whatsappUrl);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        Get.snackbar(
          'خطأ',
          'لا يمكن فتح واتساب. تأكد من تثبيت التطبيق على جهازك',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في فتح واتساب: ${e.toString()}',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Document Actions
  Future<void> printInvoice() async {
    if (_orderDetails.value == null) return;

    // TODO: PDF functionality commented out for now
    Get.snackbar(
      'طباعة الفاتورة',
      'سيتم إضافة ميزة طباعة الفاتورة قريباً',
      snackPosition: SnackPosition.bottom,
      icon: const Icon(Icons.print, color: Colors.white),
      backgroundColor: Colors.purple,
      colorText: Colors.white,
    );

    /* PDF functionality commented out
    try {
      _isUpdatingStatus.value = true;
      _error.value = null;

      // Generate PDF
      final pdf = await _generateInvoicePDF();

      // Show print dialog
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: 'فاتورة_طلب_${_orderDetails.value!.id}.pdf',
      );

      Get.snackbar(
        'تم إنشاء الفاتورة',
        'تم إنشاء فاتورة الطلب بنجاح',
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      _error.value = 'فشل في إنشاء الفاتورة: ${e.toString()}';
      Get.snackbar(
        'خطأ',
        _error.value!,
        snackPosition: SnackPosition.bottom,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isUpdatingStatus.value = false;
    }
    */
  }

  void exportData() {
    if (_orderDetails.value == null) return;

    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تصدير بيانات الطلب',
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
              title: const Text('تصدير كـ PDF'),
              onTap: () {
                Get.back();
                _exportAsPDF();
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart, color: Colors.green),
              title: const Text('تصدير كـ Excel'),
              onTap: () {
                Get.back();
                _exportAsExcel();
              },
            ),
            ListTile(
              leading: const Icon(Icons.text_snippet, color: Colors.blue),
              title: const Text('تصدير كـ CSV'),
              onTap: () {
                Get.back();
                _exportAsCSV();
              },
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Get.back(),
                child: const Text('إلغاء'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _exportAsPDF() {
    Get.snackbar(
      'تصدير PDF',
      'سيتم إضافة ميزة تصدير PDF قريباً',
      snackPosition: SnackPosition.bottom,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  void _exportAsExcel() {
    Get.snackbar(
      'تصدير Excel',
      'سيتم إضافة ميزة تصدير Excel قريباً',
      snackPosition: SnackPosition.bottom,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  void _exportAsCSV() {
    Get.snackbar(
      'تصدير CSV',
      'سيتم إضافة ميزة تصدير CSV قريباً',
      snackPosition: SnackPosition.bottom,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  // Order Management Actions
  void duplicateOrder() {
    if (_orderDetails.value == null) return;

    Get.dialog(
      AlertDialog(
        title: const Text('نسخ الطلب'),
        content: const Text('هل تريد إنشاء طلب جديد بنفس المنتجات؟'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _performDuplicateOrder();
            },
            child: const Text('نسخ الطلب'),
          ),
        ],
      ),
    );
  }

  void _performDuplicateOrder() {
    // TODO: Implement order duplication functionality
    Get.snackbar(
      'نسخ الطلب',
      'سيتم إضافة ميزة نسخ الطلب قريباً',
      snackPosition: SnackPosition.bottom,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );
  }

  void editOrder() {
    if (_orderDetails.value == null || !canEditOrder) return;

    Get.snackbar(
      'تعديل الطلب',
      'سيتم إضافة ميزة تعديل الطلب قريباً',
      snackPosition: SnackPosition.bottom,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  // Status-dependent action availability
  List<String> getAvailableActions() {
    if (_orderDetails.value == null) return [];

    final status = _orderDetails.value!.status.toLowerCase();

    switch (status) {
      case 'pending':
        return ['accept', 'edit', 'cancel'];
      case 'processing':
        return ['ship', 'return_pending'];
      case 'shipped':
        return ['deliver', 'return_processing'];
      case 'delivered':
      case 'cancelled':
        return []; // Read-only status
      default:
        return [];
    }
  }

  String getActionLabel(String action) {
    switch (action) {
      case 'accept':
        return 'قبول الطلب';
      case 'edit':
        return 'تعديل الطلب';
      case 'cancel':
        return 'إلغاء الطلب';
      case 'ship':
        return 'تم الشحن';
      case 'return_pending':
        return 'إرجاع للانتظار';
      case 'deliver':
        return 'تم التسليم';
      case 'return_processing':
        return 'إرجاع للمعالجة';
      default:
        return action;
    }
  }

  IconData getActionIcon(String action) {
    switch (action) {
      case 'accept':
        return Icons.check_circle;
      case 'edit':
        return Icons.edit;
      case 'cancel':
        return Icons.cancel;
      case 'ship':
        return Icons.local_shipping;
      case 'return_pending':
        return Icons.undo;
      case 'deliver':
        return Icons.done_all;
      case 'return_processing':
        return Icons.undo;
      default:
        return Icons.touch_app;
    }
  }

  Color getActionColor(String action) {
    switch (action) {
      case 'accept':
        return Colors.green;
      case 'edit':
        return Colors.blue;
      case 'cancel':
        return Colors.red;
      case 'ship':
        return Colors.orange;
      case 'return_pending':
        return Colors.grey;
      case 'deliver':
        return Colors.teal;
      case 'return_processing':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  void performAction(String action) {
    switch (action) {
      case 'accept':
        acceptOrder();
        break;
      case 'edit':
        editOrder();
        break;
      case 'cancel':
        cancelOrder();
        break;
      case 'ship':
        shipOrder();
        break;
      case 'return_pending':
        returnToPending();
        break;
      case 'deliver':
        deliverOrder();
        break;
      case 'return_processing':
        returnToProcessing();
        break;
    }
  }

  bool get canEditOrder {
    if (_orderDetails.value == null) return false;
    final status = _orderDetails.value!.status.toLowerCase();
    return ['pending', 'processing'].contains(status);
  }

  bool get isOrderCompleted {
    if (_orderDetails.value == null) return false;
    final status = _orderDetails.value!.status.toLowerCase();
    return ['delivered', 'cancelled'].contains(status);
  }

  // PDF Generation - Commented out for now
  /*
  Future<pw.Document> _generateInvoicePDF() async {
    final pdf = pw.Document();
    final order = _orderDetails.value!;

    // Load Arabic font
    final arabicFont = await PdfGoogleFonts.notoSansArabicRegular();
    final arabicFontBold = await PdfGoogleFonts.notoSansArabicBold();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(base: arabicFont, bold: arabicFontBold),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColors.green,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Text(
                      'فاتورة طلب',
                      style: pw.TextStyle(
                        font: arabicFontBold,
                        fontSize: 24,
                        color: PdfColors.white,
                      ),
                    ),
                    pw.SizedBox(height: 8),
                    pw.Text(
                      'رقم الطلب: ${order.id}',
                      style: pw.TextStyle(
                        font: arabicFont,
                        fontSize: 16,
                        color: PdfColors.white,
                      ),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // Order Information
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'تاريخ الطلب: ${formatDate(order.createdAt)}',
                        style: pw.TextStyle(font: arabicFont, fontSize: 12),
                      ),
                      pw.Text(
                        'حالة الطلب: ${getStatusLabel(order.status)}',
                        style: pw.TextStyle(font: arabicFont, fontSize: 12),
                      ),
                    ],
                  ),
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Text(
                        'معلومات المتجر',
                        style: pw.TextStyle(font: arabicFontBold, fontSize: 14),
                      ),
                      pw.Text(
                        order.store.name,
                        style: pw.TextStyle(font: arabicFont, fontSize: 12),
                      ),
                      pw.Text(
                        order.store.address,
                        style: pw.TextStyle(font: arabicFont, fontSize: 10),
                      ),
                    ],
                  ),
                ],
              ),

              pw.SizedBox(height: 20),

              // Items Table
              pw.Text(
                'تفاصيل المنتجات',
                style: pw.TextStyle(font: arabicFontBold, fontSize: 16),
              ),
              pw.SizedBox(height: 10),

              pw.Table(
                border: pw.TableBorder.all(),
                children: [
                  // Header row
                  pw.TableRow(
                    decoration: const pw.BoxDecoration(
                      color: PdfColors.grey200,
                    ),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'المنتج',
                          style: pw.TextStyle(
                            font: arabicFontBold,
                            fontSize: 12,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'الكمية',
                          style: pw.TextStyle(
                            font: arabicFontBold,
                            fontSize: 12,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'السعر',
                          style: pw.TextStyle(
                            font: arabicFontBold,
                            fontSize: 12,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          'الإجمالي',
                          style: pw.TextStyle(
                            font: arabicFontBold,
                            fontSize: 12,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                  // Data rows
                  ...order.orderItems.map((item) {
                    final subtotal = item.pricePerUnit * item.quantity;
                    return pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            item.product.title,
                            style: pw.TextStyle(font: arabicFont, fontSize: 10),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            '${item.quantity}',
                            style: pw.TextStyle(font: arabicFont, fontSize: 10),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            '${item.pricePerUnit.toStringAsFixed(2)} جنيه',
                            style: pw.TextStyle(font: arabicFont, fontSize: 10),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            '${subtotal.toStringAsFixed(2)} جنيه',
                            style: pw.TextStyle(font: arabicFont, fontSize: 10),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                      ],
                    );
                  }),
                ],
              ),

              pw.SizedBox(height: 20),

              // Total
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey100,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'الإجمالي النهائي',
                      style: pw.TextStyle(font: arabicFontBold, fontSize: 16),
                    ),
                    pw.Text(
                      '${order.productsTotalPrice.toStringAsFixed(2)} جنيه',
                      style: pw.TextStyle(
                        font: arabicFontBold,
                        fontSize: 16,
                        color: PdfColors.green,
                      ),
                    ),
                  ],
                ),
              ),

              pw.Spacer(),

              // Footer
              pw.Center(
                child: pw.Text(
                  'شكراً لتعاملكم معنا',
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                ),
              ),
            ],
          );
        },
      ),
    );

    return pdf;
  }
  */

  // Legacy methods for compatibility
  void shareOrder() {
    exportData();
  }

  void printOrder() {
    printInvoice();
  }
}
