import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'order_details_controller.dart';
import 'widgets/order_actions_section.dart';
import 'widgets/order_header_card.dart';
import 'widgets/order_items_section.dart';
import 'widgets/order_summary_card.dart';
import 'widgets/store_info_card.dart';

class OrderDetailsPage extends StatelessWidget {
  const OrderDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put<OrderDetailsController>(
      OrderDetailsController(),
    );

    return Scaffold(
      backgroundColor: AppColors.scaffoldWithBoxBackground,
      appBar: AppBar(
        title: Obx(
          () => Text(
            controller.orderDetails != null
                ? 'طلب #${controller.orderDetails!.id}'
                : 'تفاصيل الطلب',
          ),
        ),
        actions: [
          Obx(() {
            if (controller.orderDetails == null) return const SizedBox.shrink();

            return PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'call':
                    controller.callStore();
                    break;
                  case 'whatsapp':
                    controller.sendWhatsApp();
                    break;
                  case 'print':
                    controller.printInvoice();
                    break;
                  case 'export':
                    controller.exportData();
                    break;
                  case 'duplicate':
                    controller.duplicateOrder();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'call',
                  child: Row(
                    children: [
                      Icon(Icons.phone, color: Colors.blue),
                      SizedBox(width: 8),
                      Text('اتصال بالمتجر'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'whatsapp',
                  child: Row(
                    children: [
                      Icon(Icons.message, color: Colors.green),
                      SizedBox(width: 8),
                      Text('إرسال واتساب'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'print',
                  child: Row(
                    children: [
                      Icon(Icons.print, color: Colors.purple),
                      SizedBox(width: 8),
                      Text('طباعة فاتورة'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'export',
                  child: Row(
                    children: [
                      Icon(Icons.file_download, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('تصدير البيانات'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'duplicate',
                  child: Row(
                    children: [
                      Icon(Icons.copy, color: Colors.indigo),
                      SizedBox(width: 8),
                      Text('نسخ الطلب'),
                    ],
                  ),
                ),
              ],
            );
          }),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(color: AppColors.primary),
          );
        }

        if (controller.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  controller.error!,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: controller.refreshOrderDetails,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (controller.orderDetails == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.receipt_long_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'لم يتم العثور على تفاصيل الطلب',
                  style: Theme.of(
                    context,
                  ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.refreshOrderDetails,
          color: AppColors.primary,
          child: ListView(
            padding: const EdgeInsets.all(AppDefaults.padding),
            children: [
              // Order header with status
              OrderHeaderCard(order: controller.orderDetails!),

              const SizedBox(height: 16),

              // Store information
              StoreInfoCard(store: controller.orderDetails!.store),

              const SizedBox(height: 16),

              // Order items
              OrderItemsSection(
                items: controller.orderDetails!.orderItems,
                canEdit: controller.canEditOrder,
              ),

              const SizedBox(height: 16),

              // Order summary
              OrderSummaryCard(order: controller.orderDetails!),

              const SizedBox(height: 16),

              // Order actions
              const OrderActionsSection(),

              const SizedBox(height: 100), // Space for FAB
            ],
          ),
        );
      }),
      floatingActionButton: Obx(() {
        if (controller.orderDetails == null || controller.isOrderCompleted) {
          return const SizedBox.shrink();
        }

        final availableActions = controller.getAvailableActions();
        if (availableActions.isEmpty) {
          return const SizedBox.shrink();
        }

        // Show the primary action as FAB
        final primaryAction = availableActions.first;

        return FloatingActionButton.extended(
          onPressed: controller.isUpdatingStatus
              ? null
              : () => controller.performAction(primaryAction),
          backgroundColor: controller.getActionColor(primaryAction),
          icon: controller.isUpdatingStatus
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : Icon(
                  controller.getActionIcon(primaryAction),
                  color: Colors.white,
                ),
          label: Text(
            controller.isUpdatingStatus
                ? 'جاري التحديث...'
                : controller.getActionLabel(primaryAction),
            style: const TextStyle(color: Colors.white),
          ),
        );
      }),
    );
  }
}
