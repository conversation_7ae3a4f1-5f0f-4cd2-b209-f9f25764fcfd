import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import '../order_details_controller.dart';

// Helpers
String _formatPhoneNumber(String phone) {
  // Remove any non-digit characters
  final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

  // Format Egyptian phone numbers
  if (cleanPhone.startsWith('20')) {
    // International format: +20 xxx xxx xxxx
    if (cleanPhone.length >= 12) {
      return '+${cleanPhone.substring(0, 2)} ${cleanPhone.substring(2, 5)} ${cleanPhone.substring(5, 8)} ${cleanPhone.substring(8)}';
    }
  } else if (cleanPhone.startsWith('01')) {
    // Local format: 01x xxxx xxxx
    if (cleanPhone.length >= 11) {
      return '${cleanPhone.substring(0, 3)} ${cleanPhone.substring(3, 7)} ${cleanPhone.substring(7)}';
    }
  }

  // Return original if formatting fails
  return phone;
}

void _makePhoneCall(String phoneNumber) {
  // Use the controller's call functionality
  final controller = Get.find<OrderDetailsController>();
  controller.callStore();
}

class StoreInfoCard extends StatelessWidget {
  final StoreOut store;

  const StoreInfoCard({super.key, required this.store});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.store, color: AppColors.primary, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات المتجر',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      store.name,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Store details
          _StoreDetailRow(
            icon: Icons.location_on,
            label: 'العنوان',
            value: store.address,
          ),

          const SizedBox(height: 12),

          // Store owner phone number
          if (store.ownerPhone.isNotEmpty)
            _StoreDetailRow(
              icon: Icons.phone,
              label: 'رقم الهاتف',
              value: _formatPhoneNumber(store.ownerPhone),
              isClickable: true,
              onTap: () => _makePhoneCall(store.ownerPhone),
            ),

          // Store owner info
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.person, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  'اسم المستخدم: ',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
                Text(
                  store.ownerUsername,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _StoreDetailRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final bool isClickable;
  final VoidCallback? onTap;

  const _StoreDetailRow({
    required this.icon,
    required this.label,
    required this.value,
    this.isClickable = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
              const SizedBox(height: 2),
              isClickable && onTap != null
                  ? InkWell(
                      onTap: onTap,
                      child: Text(
                        value,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.primary,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    )
                  : Text(
                      value,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
            ],
          ),
        ),
      ],
    );
  }
}
