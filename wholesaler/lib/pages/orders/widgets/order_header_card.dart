import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import '../order_details_controller.dart';

class OrderHeaderCard extends StatelessWidget {
  final OrderDetailOut order;

  const OrderHeaderCard({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderDetailsController>();

    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order ID and Status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'طلب #${order.id}',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: controller
                      .getStatusColor(order.status)
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: controller
                        .getStatusColor(order.status)
                        .withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      controller.getStatusIcon(order.status),
                      color: controller.getStatusColor(order.status),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      controller.getStatusLabel(order.status),
                      style: TextStyle(
                        color: controller.getStatusColor(order.status),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Order dates
          Row(
            children: [
              Expanded(
                child: _InfoItem(
                  icon: Icons.access_time,
                  label: 'تاريخ الطلب',
                  value: controller.formatDate(order.createdAt),
                ),
              ),
              if (order.updatedAt != order.createdAt) ...[
                const SizedBox(width: 16),
                Expanded(
                  child: _InfoItem(
                    icon: Icons.update,
                    label: 'آخر تحديث',
                    value: controller.formatDate(order.updatedAt),
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 16),

          // Order summary info
          Row(
            children: [
              Expanded(
                child: _InfoItem(
                  icon: Icons.shopping_cart,
                  label: 'عدد المنتجات',
                  value: '${order.productsTotalQuantity} منتج',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _InfoItem(
                  icon: Icons.attach_money,
                  label: 'إجمالي المبلغ',
                  value: '${order.totalPrice.toStringAsFixed(2)} جنيه',
                  valueColor: AppColors.primary,
                ),
              ),
            ],
          ),

          // Order notes placeholder (not available in current API model)
          // Notes functionality can be added when the API supports it
        ],
      ),
    );
  }
}

class _InfoItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color? valueColor;

  const _InfoItem({
    required this.icon,
    required this.label,
    required this.value,
    this.valueColor,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: valueColor ?? Colors.black87,
          ),
        ),
      ],
    );
  }
}
