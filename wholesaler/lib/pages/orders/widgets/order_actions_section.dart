import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import '../order_details_controller.dart';

class OrderActionsSection extends StatelessWidget {
  const OrderActionsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OrderDetailsController>();

    return Obx(() {
      if (controller.orderDetails == null) return const SizedBox.shrink();

      final availableActions = controller.getAvailableActions();
      
      if (availableActions.isEmpty) {
        return _buildReadOnlyStatus(controller);
      }

      return Container(
        padding: const EdgeInsets.all(AppDefaults.padding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: AppDefaults.borderRadius,
          boxShadow: AppDefaults.boxShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.settings,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'إجراءات الطلب',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Action buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: availableActions.map((action) {
                return _ActionButton(
                  action: action,
                  label: controller.getActionLabel(action),
                  icon: controller.getActionIcon(action),
                  color: controller.getActionColor(action),
                  onPressed: () => controller.performAction(action),
                  isLoading: controller.isUpdatingStatus,
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // Communication and document actions
            _buildSecondaryActions(controller),
          ],
        ),
      );
    });
  }

  Widget _buildReadOnlyStatus(OrderDetailsController controller) {
    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: AppDefaults.borderRadius,
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Icon(
            controller.getStatusIcon(controller.orderDetails!.status),
            color: controller.getStatusColor(controller.orderDetails!.status),
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حالة الطلب',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  controller.getStatusLabel(controller.orderDetails!.status),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: controller.getStatusColor(controller.orderDetails!.status),
                  ),
                ),
              ],
            ),
          ),
          if (controller.orderDetails!.status.toLowerCase() == 'delivered')
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(
                'مكتمل',
                style: TextStyle(
                  color: Colors.green,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSecondaryActions(OrderDetailsController controller) {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: 8),
        
        // Communication actions
        Row(
          children: [
            Expanded(
              child: _SecondaryActionButton(
                icon: Icons.phone,
                label: 'اتصال',
                color: Colors.blue,
                onPressed: controller.callStore,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _SecondaryActionButton(
                icon: Icons.message,
                label: 'واتساب',
                color: Colors.green,
                onPressed: controller.sendWhatsApp,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Document actions
        Row(
          children: [
            Expanded(
              child: _SecondaryActionButton(
                icon: Icons.print,
                label: 'طباعة',
                color: Colors.purple,
                onPressed: controller.printInvoice,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _SecondaryActionButton(
                icon: Icons.file_download,
                label: 'تصدير',
                color: Colors.orange,
                onPressed: controller.exportData,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Order management actions
        Row(
          children: [
            Expanded(
              child: _SecondaryActionButton(
                icon: Icons.copy,
                label: 'نسخ الطلب',
                color: Colors.indigo,
                onPressed: controller.duplicateOrder,
              ),
            ),
            if (controller.canEditOrder) ...[
              const SizedBox(width: 8),
              Expanded(
                child: _SecondaryActionButton(
                  icon: Icons.edit,
                  label: 'تعديل',
                  color: Colors.blue,
                  onPressed: controller.editOrder,
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }
}

class _ActionButton extends StatelessWidget {
  final String action;
  final String label;
  final IconData icon;
  final Color color;
  final VoidCallback onPressed;
  final bool isLoading;

  const _ActionButton({
    required this.action,
    required this.label,
    required this.icon,
    required this.color,
    required this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: isLoading ? null : onPressed,
      icon: isLoading
          ? SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Colors.white,
              ),
            )
          : Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

class _SecondaryActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onPressed;

  const _SecondaryActionButton({
    required this.icon,
    required this.label,
    required this.color,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16, color: color),
      label: Text(
        label,
        style: TextStyle(color: color, fontSize: 12),
      ),
      style: OutlinedButton.styleFrom(
        side: BorderSide(color: color.withValues(alpha: 0.3)),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
      ),
    );
  }
}
