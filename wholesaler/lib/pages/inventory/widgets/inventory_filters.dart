import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import '../inventory_controller.dart';

class InventoryFilters extends StatelessWidget {
  const InventoryFilters({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<InventoryController>();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'ابحث في المنتجات...',
                prefixIcon: const Icon(Icons.search, color: AppColors.primary),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.tune, color: AppColors.primary),
                  onPressed: () => _showFilterBottomSheet(context, controller),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.primary),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: controller.setSearchQuery,
            ),
          ),
          
          const SizedBox(height: 12),
          
          // Quick filter chips
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
              itemCount: controller.stockFilterOptions.length,
              itemBuilder: (context, index) {
                final option = controller.stockFilterOptions[index];
                final key = option['key'] as String;
                final label = option['label'] as String;
                
                return Obx(() {
                  final isSelected = controller.selectedStockFilter == key;
                  
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(
                        label,
                        style: TextStyle(
                          color: isSelected ? Colors.white : AppColors.primary,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        ),
                      ),
                      selected: isSelected,
                      onSelected: (_) => controller.setStockFilter(key),
                      backgroundColor: Colors.white,
                      selectedColor: AppColors.primary,
                      checkmarkColor: Colors.white,
                      side: BorderSide(
                        color: isSelected ? AppColors.primary : Colors.grey[300]!,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    ),
                  );
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet(BuildContext context, InventoryController controller) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) => Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Title
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'فلترة المنتجات',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      controller.setStockFilter('all');
                      controller.setCategoryFilter(null);
                      controller.setCompanyFilter(null);
                      controller.setSortOption('-created_at');
                    },
                    child: const Text('إعادة تعيين'),
                  ),
                ],
              ),
            ),
            
            // Filters content
            Expanded(
              child: ListView(
                controller: scrollController,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                children: [
                  // Sort options
                  Text(
                    'ترتيب حسب',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: controller.sortOptions.map((option) {
                      final key = option['key'] as String;
                      final label = option['label'] as String;
                      final isSelected = controller.selectedSort == key;
                      
                      return FilterChip(
                        label: Text(label),
                        selected: isSelected,
                        onSelected: (_) => controller.setSortOption(key),
                        backgroundColor: Colors.grey[100],
                        selectedColor: AppColors.primary.withValues(alpha: 0.1),
                        checkmarkColor: AppColors.primary,
                        side: BorderSide(
                          color: isSelected ? AppColors.primary : Colors.grey[300]!,
                        ),
                      );
                    }).toList(),
                  )),
                  
                  const SizedBox(height: 24),
                  
                  // Stock status
                  Text(
                    'حالة المخزون',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: controller.stockFilterOptions.map((option) {
                      final key = option['key'] as String;
                      final label = option['label'] as String;
                      final isSelected = controller.selectedStockFilter == key;
                      
                      return FilterChip(
                        label: Text(label),
                        selected: isSelected,
                        onSelected: (_) => controller.setStockFilter(key),
                        backgroundColor: Colors.grey[100],
                        selectedColor: AppColors.primary.withValues(alpha: 0.1),
                        checkmarkColor: AppColors.primary,
                        side: BorderSide(
                          color: isSelected ? AppColors.primary : Colors.grey[300]!,
                        ),
                      );
                    }).toList(),
                  )),
                  
                  const SizedBox(height: 24),
                  
                  // Companies (placeholder - would be populated from API)
                  Text(
                    'الشركة',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'سيتم تحميل قائمة الشركات من الخادم',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Categories (placeholder - would be populated from API)
                  Text(
                    'الفئة',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'سيتم تحميل قائمة الفئات من الخادم',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                ],
              ),
            ),
            
            // Apply button
            Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('تطبيق الفلاتر'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
