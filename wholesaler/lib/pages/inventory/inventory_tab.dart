import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'inventory_controller.dart';
import 'widgets/inventory_card.dart';
import 'widgets/inventory_filters.dart';

class InventoryTab extends StatelessWidget {
  const InventoryTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put<InventoryController>(InventoryController());

    return Scaffold(
      backgroundColor: AppColors.scaffoldWithBoxBackground,
      body: Column(
        children: [
          // Filters and search
          Container(
            color: Colors.white,
            child: const InventoryFilters(),
          ),
          
          // Stats row
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(AppDefaults.padding),
            child: Obx(() {
              if (controller.stats == null) {
                return const SizedBox.shrink();
              }
              
              final stats = controller.stats!;
              return Row(
                children: [
                  Expanded(
                    child: _StatCard(
                      title: 'إجمالي المنتجات',
                      value: '${stats.totalItems}',
                      color: AppColors.primary,
                      icon: Icons.inventory,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _StatCard(
                      title: 'مخزون منخفض',
                      value: '${stats.lowStockItems}',
                      color: Colors.orange,
                      icon: Icons.warning,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _StatCard(
                      title: 'نفد المخزون',
                      value: '${stats.outOfStockItems}',
                      color: Colors.red,
                      icon: Icons.error,
                    ),
                  ),
                ],
              );
            }),
          ),
          
          // Items list
          Expanded(
            child: Obx(() {
              if (controller.isLoading && controller.items.isEmpty) {
                return const Center(
                  child: CircularProgressIndicator(
                    color: AppColors.primary,
                  ),
                );
              }

              if (controller.error != null) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        controller.error!,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: controller.refreshItems,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                );
              }

              if (controller.items.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.inventory_2_outlined,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد منتجات',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'لم يتم العثور على أي منتجات بالفلتر المحدد',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[500],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: controller.refreshItems,
                color: AppColors.primary,
                child: ListView.builder(
                  padding: const EdgeInsets.all(AppDefaults.padding),
                  itemCount: controller.items.length + (controller.hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == controller.items.length) {
                      // Load more indicator
                      if (controller.isLoadingMore) {
                        return const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(
                            child: CircularProgressIndicator(
                              color: AppColors.primary,
                            ),
                          ),
                        );
                      } else {
                        // Load more button
                        return Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Center(
                            child: ElevatedButton(
                              onPressed: controller.loadMoreItems,
                              child: const Text('تحميل المزيد'),
                            ),
                          ),
                        );
                      }
                    }

                    final item = controller.items[index];
                    return InventoryCard(
                      item: item,
                      onTap: () => controller.viewItemDetails(item.id),
                      onUpdateQuantity: (newQuantity) => 
                          controller.updateItemQuantity(item.id, newQuantity),
                      onUpdatePrice: (newPrice) => 
                          controller.updateItemPrice(item.id, newPrice),
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final Color color;
  final IconData icon;

  const _StatCard({
    required this.title,
    required this.value,
    required this.color,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 11,
              color: color,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
