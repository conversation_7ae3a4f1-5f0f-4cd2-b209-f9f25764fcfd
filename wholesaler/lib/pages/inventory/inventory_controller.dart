import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import '../../services/wholesaler_service.dart';

// Mock class for ItemStatsOut until it's available in the API
class MockItemStats {
  final int totalItems;
  final int lowStockItems;
  final int outOfStockItems;

  MockItemStats({
    required this.totalItems,
    required this.lowStockItems,
    required this.outOfStockItems,
  });
}

class InventoryController extends GetxController {
  final WholesalerService _wholesalerService = Get.find<WholesalerService>();

  // Filter states
  final _searchQuery = ''.obs;
  final _selectedCompany = Rx<int?>(null);
  final _selectedCategory = Rx<int?>(null);
  final _selectedStockFilter = 'all'.obs;
  final _selectedSort = '-created_at'.obs;

  // Pagination
  final _currentPage = 1.obs;
  final _hasMore = true.obs;
  final _isLoadingMore = false.obs;

  // Stats
  final _stats = Rx<MockItemStats?>(null);
  final _companies = <CompanyOut>[].obs;
  final _categories = <CategoryOut>[].obs;

  // Getters
  String get searchQuery => _searchQuery.value;
  int? get selectedCompany => _selectedCompany.value;
  int? get selectedCategory => _selectedCategory.value;
  String get selectedStockFilter => _selectedStockFilter.value;
  String get selectedSort => _selectedSort.value;
  List<ItemOut> get items => _wholesalerService.items;
  bool get isLoading => _wholesalerService.isLoading;
  bool get isLoadingMore => _isLoadingMore.value;
  bool get hasMore => _hasMore.value;
  String? get error => _wholesalerService.error;
  MockItemStats? get stats => _stats.value;
  List<CompanyOut> get companies => _companies;
  List<CategoryOut> get categories => _categories;

  // Filter options
  final stockFilterOptions = [
    {'key': 'all', 'label': 'الكل'},
    {'key': 'in_stock', 'label': 'متوفر'},
    {'key': 'low_stock', 'label': 'مخزون منخفض'},
    {'key': 'out_of_stock', 'label': 'نفد المخزون'},
  ];

  final sortOptions = [
    {'key': '-created_at', 'label': 'الأحدث'},
    {'key': 'created_at', 'label': 'الأقدم'},
    {'key': 'product__name', 'label': 'الاسم (أ-ي)'},
    {'key': '-product__name', 'label': 'الاسم (ي-أ)'},
    {'key': 'base_price', 'label': 'السعر (الأقل)'},
    {'key': '-base_price', 'label': 'السعر (الأعلى)'},
    {'key': 'inventory_count', 'label': 'الكمية (الأقل)'},
    {'key': '-inventory_count', 'label': 'الكمية (الأكثر)'},
  ];

  @override
  void onInit() {
    super.onInit();
    loadItems();
  }

  // Filter methods
  void setSearchQuery(String query) {
    _searchQuery.value = query;
    _currentPage.value = 1;
    _hasMore.value = true;
    loadItems();
  }

  void setCompanyFilter(int? companyId) {
    _selectedCompany.value = companyId;
    _currentPage.value = 1;
    _hasMore.value = true;
    loadItems();
  }

  void setCategoryFilter(int? categoryId) {
    _selectedCategory.value = categoryId;
    _currentPage.value = 1;
    _hasMore.value = true;
    loadItems();
  }

  void setStockFilter(String filter) {
    if (_selectedStockFilter.value != filter) {
      _selectedStockFilter.value = filter;
      _currentPage.value = 1;
      _hasMore.value = true;
      loadItems();
    }
  }

  void setSortOption(String sort) {
    if (_selectedSort.value != sort) {
      _selectedSort.value = sort;
      _currentPage.value = 1;
      _hasMore.value = true;
      loadItems();
    }
  }

  // Data loading methods
  Future<void> loadItems() async {
    _currentPage.value = 1;

    // Convert stock filter to API parameter
    String? stockParam;
    switch (_selectedStockFilter.value) {
      case 'in_stock':
        stockParam = 'in_stock';
        break;
      case 'low_stock':
        stockParam = 'low_stock';
        break;
      case 'out_of_stock':
        stockParam = 'out_of_stock';
        break;
      default:
        stockParam = null;
    }

    await _wholesalerService.loadItems(
      page: _currentPage.value,
      search: _searchQuery.value.isEmpty ? null : _searchQuery.value,
      company: _selectedCompany.value,
      category: _selectedCategory.value,
      stock: stockParam,
      sort: _selectedSort.value,
    );

    // Update pagination state based on response
    _hasMore.value = items.length >= 20; // Assuming page size is 20

    // Update additional data from response if available
    // Note: This would need to be implemented in the service to return full response
    _updateAdditionalData();
  }

  Future<void> loadMoreItems() async {
    if (_isLoadingMore.value || !_hasMore.value) return;

    _isLoadingMore.value = true;
    _currentPage.value++;

    try {
      final initialCount = items.length;

      String? stockParam;
      switch (_selectedStockFilter.value) {
        case 'in_stock':
          stockParam = 'in_stock';
          break;
        case 'low_stock':
          stockParam = 'low_stock';
          break;
        case 'out_of_stock':
          stockParam = 'out_of_stock';
          break;
        default:
          stockParam = null;
      }

      await _wholesalerService.loadItems(
        page: _currentPage.value,
        search: _searchQuery.value.isEmpty ? null : _searchQuery.value,
        company: _selectedCompany.value,
        category: _selectedCategory.value,
        stock: stockParam,
        sort: _selectedSort.value,
      );

      // Check if we got new items
      final newItemsCount = items.length - initialCount;
      _hasMore.value = newItemsCount >= 20; // Assuming page size is 20
    } finally {
      _isLoadingMore.value = false;
    }
  }

  Future<void> refreshItems() async {
    _wholesalerService.clearError();
    await loadItems();
  }

  // Item actions
  Future<void> updateItemQuantity(int itemId, int newQuantity) async {
    await _wholesalerService.updateItem(
      itemId,
      ItemUpdateIn(inventoryCount: newQuantity),
    );

    if (_wholesalerService.error == null) {
      Get.snackbar(
        'تم التحديث',
        'تم تحديث كمية المنتج بنجاح',
        snackPosition: SnackPosition.bottom,
      );
    } else {
      Get.snackbar(
        'خطأ',
        _wholesalerService.error!,
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  Future<void> updateItemPrice(int itemId, double newPrice) async {
    await _wholesalerService.updateItem(
      itemId,
      ItemUpdateIn(basePrice: newPrice),
    );

    if (_wholesalerService.error == null) {
      Get.snackbar(
        'تم التحديث',
        'تم تحديث سعر المنتج بنجاح',
        snackPosition: SnackPosition.bottom,
      );
    } else {
      Get.snackbar(
        'خطأ',
        _wholesalerService.error!,
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  void viewItemDetails(int itemId) {
    Get.snackbar(
      'تفاصيل المنتج',
      'سيتم فتح تفاصيل المنتج رقم $itemId',
      snackPosition: SnackPosition.bottom,
    );
  }

  // Utility methods
  void _updateAdditionalData() {
    // This would be populated from the API response
    // For now, we'll create mock data
    _stats.value = MockItemStats(
      totalItems: items.length,
      lowStockItems: items.where((item) => item.inventoryCount < 10).length,
      outOfStockItems: items.where((item) => item.inventoryCount == 0).length,
    );
  }

  String getStockStatusLabel(int quantity) {
    if (quantity == 0) return 'نفد المخزون';
    if (quantity < 10) return 'مخزون منخفض';
    return 'متوفر';
  }

  Color getStockStatusColor(int quantity) {
    if (quantity == 0) return Colors.red;
    if (quantity < 10) return Colors.orange;
    return Colors.green;
  }

  String getSortLabel(String key) {
    final option = sortOptions.firstWhere(
      (option) => option['key'] == key,
      orElse: () => {'label': 'غير معروف'},
    );
    return option['label'] as String;
  }

  String getStockFilterLabel(String key) {
    final option = stockFilterOptions.firstWhere(
      (option) => option['key'] == key,
      orElse: () => {'label': 'غير معروف'},
    );
    return option['label'] as String;
  }
}
