import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_icons.dart';
import 'wholesaler_home_controller.dart';
import '../orders/orders_tab.dart';
import '../inventory/inventory_tab.dart';
import '../reports/reports_tab.dart';
import '../notifications/notifications_tab.dart';

class WholesalerHomePage extends StatelessWidget {
  const WholesalerHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put<WholesalerHomeController>(
      WholesalerHomeController(),
    );

    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        bottomNavigationBar: Obx(
          () => BottomNavigationBar(
            currentIndex: controller.currentIndex,
            onTap: controller.changeTab,
            type: BottomNavigationBarType.fixed,
            backgroundColor: AppColors.scaffoldBackground,
            selectedItemColor: AppColors.primary,
            unselectedItemColor: AppColors.placeholder,
            selectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 12,
            ),
            items: [
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  AppIcons.orderProcessing,
                  colorFilter: ColorFilter.mode(
                    controller.currentIndex == 0
                        ? AppColors.primary
                        : AppColors.placeholder,
                    BlendMode.srcIn,
                  ),
                ),
                label: 'الطلبات',
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  AppIcons.shoppingBag,
                  colorFilter: ColorFilter.mode(
                    controller.currentIndex == 1
                        ? AppColors.primary
                        : AppColors.placeholder,
                    BlendMode.srcIn,
                  ),
                ),
                label: 'المخزون',
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  AppIcons.dashboardIcon,
                  colorFilter: ColorFilter.mode(
                    controller.currentIndex == 2
                        ? AppColors.primary
                        : AppColors.placeholder,
                    BlendMode.srcIn,
                  ),
                ),
                label: 'التقارير',
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  AppIcons.profileNotification,
                  colorFilter: ColorFilter.mode(
                    controller.currentIndex == 3
                        ? AppColors.primary
                        : AppColors.placeholder,
                    BlendMode.srcIn,
                  ),
                ),
                label: 'الإشعارات',
              ),
            ],
          ),
        ),
        appBar: AppBar(
          title: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(AppIcons.logoCropped, height: 32),
              const SizedBox(width: 11),
              Text(
                "تاجر بلس - الجملة",
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 8, top: 4, bottom: 4),
              child: ElevatedButton(
                onPressed: () {
                  Get.snackbar(
                    'الملف الشخصي',
                    'سيتم فتح الملف الشخصي للتاجر',
                    snackPosition: SnackPosition.bottom,
                  );
                },
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(0),
                  backgroundColor: const Color(0xFFF2F6F3),
                  shape: const CircleBorder(),
                ),
                child: SvgPicture.asset(
                  AppIcons.profile,
                  fit: BoxFit.fitHeight,
                ),
              ),
            ),
          ],
        ),
        body: Obx(
          () => IndexedStack(
            index: controller.currentIndex,
            children: [
              const OrdersTab(),
              const InventoryTab(),
              const ReportsTab(),
              // NotificationsTab(),
              // coming soon
              Center(
                child: Text(
                  'coming soon',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
