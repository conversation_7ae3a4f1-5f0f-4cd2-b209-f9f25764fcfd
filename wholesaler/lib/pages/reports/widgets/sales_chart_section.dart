import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import '../reports_controller.dart';

class SalesChartSection extends StatelessWidget {
  final List<DailySalesOut> dailySales;

  const SalesChartSection({
    super.key,
    required this.dailySales,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ReportsController>();

    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'مبيعات الأسبوع',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '+${controller.getSalesGrowthPercentage().toStringAsFixed(1)}%',
                  style: const TextStyle(
                    color: AppColors.primary,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Summary stats
          Row(
            children: [
              Expanded(
                child: _SummaryItem(
                  title: 'إجمالي المبيعات',
                  value: controller.getTotalSalesThisWeekText(),
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _SummaryItem(
                  title: 'متوسط يومي',
                  value: controller.getAverageDailySalesText(),
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Best day
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.star,
                  color: Colors.green[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'أفضل يوم: ${controller.getHighestSalesDayText()}',
                  style: TextStyle(
                    color: Colors.green[700],
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Simple bar chart
          if (dailySales.isNotEmpty) _buildSimpleChart(context),
        ],
      ),
    );
  }

  Widget _buildSimpleChart(BuildContext context) {
    final maxSales = dailySales.map((e) => e.sales).reduce((a, b) => a > b ? a : b);
    
    return Column(
      children: [
        // Chart
        SizedBox(
          height: 120,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: dailySales.map((sale) {
              final height = maxSales > 0 ? (sale.sales / maxSales) * 100 : 0.0;
              
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Value label
                      if (sale.sales > 0)
                        Text(
                          sale.sales.toStringAsFixed(0),
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primary,
                          ),
                        ),
                      
                      const SizedBox(height: 4),
                      
                      // Bar
                      Container(
                        height: height.clamp(4.0, 100.0),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(4),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Date labels
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: dailySales.map((sale) {
            return Expanded(
              child: Text(
                _formatDateLabel(sale.date),
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  String _formatDateLabel(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
      return dayNames[date.weekday % 7];
    } catch (e) {
      return dateString.substring(dateString.length - 2);
    }
  }
}

class _SummaryItem extends StatelessWidget {
  final String title;
  final String value;
  final Color color;

  const _SummaryItem({
    required this.title,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
