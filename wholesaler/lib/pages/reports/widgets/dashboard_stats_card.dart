import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import '../reports_controller.dart';

class DashboardStatsCard extends StatelessWidget {
  final DashboardStatsOut stats;

  const DashboardStatsCard({
    super.key,
    required this.stats,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ReportsController>();

    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الإحصائيات العامة',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: controller.isPerformingWell() 
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  controller.isPerformingWell() ? 'أداء ممتاز' : 'يحتاج تحسين',
                  style: TextStyle(
                    color: controller.isPerformingWell() ? Colors.green : Colors.orange,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Stats grid
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.5,
            children: [
              _StatItem(
                icon: Icons.receipt_long,
                title: 'إجمالي الطلبات',
                value: stats.totalOrders.toString(),
                color: AppColors.primary,
                growth: controller.getOrdersGrowthPercentage(),
              ),
              _StatItem(
                icon: Icons.pending_actions,
                title: 'طلبات معلقة',
                value: stats.pendingOrders.toString(),
                color: Colors.orange,
                subtitle: '${((stats.pendingOrders / (stats.totalOrders == 0 ? 1 : stats.totalOrders)) * 100).toStringAsFixed(1)}% من الإجمالي',
              ),
              _StatItem(
                icon: Icons.inventory,
                title: 'إجمالي المنتجات',
                value: stats.totalItems.toString(),
                color: Colors.blue,
              ),
              _StatItem(
                icon: Icons.warning,
                title: 'مخزون منخفض',
                value: stats.lowStockItems.toString(),
                color: Colors.red,
                subtitle: '${((stats.lowStockItems / (stats.totalItems == 0 ? 1 : stats.totalItems)) * 100).toStringAsFixed(1)}% من الإجمالي',
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Performance message
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: controller.isPerformingWell() 
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: controller.isPerformingWell() 
                    ? Colors.green.withValues(alpha: 0.3)
                    : Colors.orange.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  controller.isPerformingWell() ? Icons.check_circle : Icons.info,
                  color: controller.isPerformingWell() ? Colors.green : Colors.orange,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    controller.getPerformanceMessage(),
                    style: TextStyle(
                      color: controller.isPerformingWell() ? Colors.green[700] : Colors.orange[700],
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _StatItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String value;
  final Color color;
  final String? subtitle;
  final double? growth;

  const _StatItem({
    required this.icon,
    required this.title,
    required this.value,
    required this.color,
    this.subtitle,
    this.growth,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(
                icon,
                color: color,
                size: 24,
              ),
              if (growth != null)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: growth! >= 0 ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${growth! >= 0 ? '+' : ''}${growth!.toStringAsFixed(1)}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle!,
              style: TextStyle(
                fontSize: 10,
                color: color.withValues(alpha: 0.7),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}
