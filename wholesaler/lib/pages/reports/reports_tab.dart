import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'reports_controller.dart';
import 'widgets/dashboard_stats_card.dart';
import 'widgets/recent_orders_section.dart';
import 'widgets/sales_chart_section.dart';

class ReportsTab extends StatelessWidget {
  const ReportsTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put<ReportsController>(ReportsController());

    return Scaffold(
      backgroundColor: AppColors.scaffoldWithBoxBackground,
      body: Obx(() {
        if (controller.isLoading && controller.dashboard == null) {
          return const Center(
            child: CircularProgressIndicator(
              color: AppColors.primary,
            ),
          );
        }

        if (controller.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  controller.error!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: controller.refreshDashboard,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (controller.dashboard == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.analytics_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد بيانات',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'لم يتم العثور على بيانات التقارير',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.refreshDashboard,
          color: AppColors.primary,
          child: ListView(
            padding: const EdgeInsets.all(AppDefaults.padding),
            children: [
              // Page title
              Text(
                'لوحة التحكم',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Dashboard stats
              DashboardStatsCard(stats: controller.dashboard!.stats),
              
              const SizedBox(height: 20),
              
              // Sales chart
              SalesChartSection(dailySales: controller.dashboard!.dailySales),
              
              const SizedBox(height: 20),
              
              // Recent orders
              RecentOrdersSection(recentOrders: controller.dashboard!.recentOrders),
              
              const SizedBox(height: 20),
              
              // Additional reports section
              _buildAdditionalReports(context),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildAdditionalReports(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تقارير إضافية',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Report options
          _ReportOption(
            icon: Icons.trending_up,
            title: 'تقرير المبيعات الشهرية',
            subtitle: 'عرض تفصيلي للمبيعات خلال الشهر الحالي',
            onTap: () {
              Get.snackbar(
                'قريباً',
                'سيتم إضافة تقرير المبيعات الشهرية قريباً',
                snackPosition: SnackPosition.bottom,
              );
            },
          ),
          
          const Divider(),
          
          _ReportOption(
            icon: Icons.inventory,
            title: 'تقرير المخزون',
            subtitle: 'حالة المخزون والمنتجات الأكثر مبيعاً',
            onTap: () {
              Get.snackbar(
                'قريباً',
                'سيتم إضافة تقرير المخزون قريباً',
                snackPosition: SnackPosition.bottom,
              );
            },
          ),
          
          const Divider(),
          
          _ReportOption(
            icon: Icons.people,
            title: 'تقرير العملاء',
            subtitle: 'إحصائيات العملاء والمتاجر الأكثر طلباً',
            onTap: () {
              Get.snackbar(
                'قريباً',
                'سيتم إضافة تقرير العملاء قريباً',
                snackPosition: SnackPosition.bottom,
              );
            },
          ),
          
          const Divider(),
          
          _ReportOption(
            icon: Icons.download,
            title: 'تصدير التقارير',
            subtitle: 'تحميل التقارير بصيغة PDF أو Excel',
            onTap: () {
              Get.snackbar(
                'قريباً',
                'سيتم إضافة ميزة تصدير التقارير قريباً',
                snackPosition: SnackPosition.bottom,
              );
            },
          ),
        ],
      ),
    );
  }
}

class _ReportOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _ReportOption({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              
              const SizedBox(width: 16),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
