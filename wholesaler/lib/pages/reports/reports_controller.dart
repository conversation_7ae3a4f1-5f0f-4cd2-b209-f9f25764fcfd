import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import '../../services/wholesaler_service.dart';

class ReportsController extends GetxController {
  final WholesalerService _wholesalerService = Get.find<WholesalerService>();

  // Getters
  DashboardOut? get dashboard => _wholesalerService.dashboard;
  bool get isLoading => _wholesalerService.isLoading;
  String? get error => _wholesalerService.error;

  @override
  void onInit() {
    super.onInit();
    loadDashboard();
  }

  // Data loading methods
  Future<void> loadDashboard() async {
    await _wholesalerService.loadDashboard();
  }

  Future<void> refreshDashboard() async {
    _wholesalerService.clearError();
    await loadDashboard();
  }

  // Utility methods for dashboard data
  String getTotalOrdersText() {
    if (dashboard?.stats == null) return '0';
    return dashboard!.stats.totalOrders.toString();
  }

  String getPendingOrdersText() {
    if (dashboard?.stats == null) return '0';
    return dashboard!.stats.pendingOrders.toString();
  }

  String getTotalItemsText() {
    if (dashboard?.stats == null) return '0';
    return dashboard!.stats.totalItems.toString();
  }

  String getLowStockItemsText() {
    if (dashboard?.stats == null) return '0';
    return dashboard!.stats.lowStockItems.toString();
  }

  double getTotalSalesThisWeek() {
    if (dashboard?.dailySales == null) return 0.0;
    return dashboard!.dailySales.fold(0.0, (sum, sale) => sum + sale.sales);
  }

  String getTotalSalesThisWeekText() {
    final total = getTotalSalesThisWeek();
    return '${total.toStringAsFixed(2)} جنيه';
  }

  double getAverageDailySales() {
    if (dashboard?.dailySales == null || dashboard!.dailySales.isEmpty) {
      return 0.0;
    }
    final total = getTotalSalesThisWeek();
    return total / dashboard!.dailySales.length;
  }

  String getAverageDailySalesText() {
    final average = getAverageDailySales();
    return '${average.toStringAsFixed(2)} جنيه';
  }

  // Get highest sales day
  DailySalesOut? getHighestSalesDay() {
    if (dashboard?.dailySales == null || dashboard!.dailySales.isEmpty) {
      return null;
    }

    DailySalesOut highest = dashboard!.dailySales.first;
    for (final sale in dashboard!.dailySales) {
      if (sale.sales > highest.sales) {
        highest = sale;
      }
    }
    return highest;
  }

  String getHighestSalesDayText() {
    final highest = getHighestSalesDay();
    if (highest == null) return 'لا توجد بيانات';

    try {
      final date = DateTime.parse(highest.date);
      final dayName = _getDayName(date.weekday);
      return '$dayName (${highest.sales.toStringAsFixed(2)} جنيه)';
    } catch (e) {
      return '${highest.date} (${highest.sales.toStringAsFixed(2)} جنيه)';
    }
  }

  // Get recent orders count
  int getRecentOrdersCount() {
    if (dashboard?.recentOrders == null) return 0;
    return dashboard!.recentOrders.length;
  }

  // Get orders by status
  Map<String, int> getOrdersByStatus() {
    if (dashboard?.recentOrders == null) return {};

    final Map<String, int> statusCount = {};
    for (final order in dashboard!.recentOrders) {
      statusCount[order.status] = (statusCount[order.status] ?? 0) + 1;
    }
    return statusCount;
  }

  // Calculate growth percentage (mock calculation)
  double getOrdersGrowthPercentage() {
    // This would typically compare with previous period data
    // For now, return a mock positive growth
    return 12.5;
  }

  double getSalesGrowthPercentage() {
    // This would typically compare with previous period data
    // For now, return a mock positive growth
    return 8.3;
  }

  // Format date helpers
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'الاثنين';
      case 2:
        return 'الثلاثاء';
      case 3:
        return 'الأربعاء';
      case 4:
        return 'الخميس';
      case 5:
        return 'الجمعة';
      case 6:
        return 'السبت';
      case 7:
        return 'الأحد';
      default:
        return 'غير معروف';
    }
  }

  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}';
    } catch (e) {
      return dateString;
    }
  }

  // Status helpers
  String getStatusLabel(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'قيد الانتظار';
      case 'confirmed':
        return 'مؤكد';
      case 'preparing':
        return 'قيد التحضير';
      case 'ready':
        return 'جاهز';
      case 'delivered':
        return 'تم التسليم';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'preparing':
        return Colors.purple;
      case 'ready':
        return Colors.green;
      case 'delivered':
        return Colors.teal;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // Performance indicators
  bool isPerformingWell() {
    final pendingRatio =
        dashboard?.stats.pendingOrders ??
        0 / (dashboard?.stats.totalOrders ?? 1);
    final lowStockRatio =
        dashboard?.stats.lowStockItems ??
        0 / (dashboard?.stats.totalItems ?? 1);

    // Consider performing well if less than 30% pending orders and less than 20% low stock
    return pendingRatio < 0.3 && lowStockRatio < 0.2;
  }

  String getPerformanceMessage() {
    if (isPerformingWell()) {
      return 'أداء ممتاز! استمر في العمل الجيد';
    } else {
      return 'يمكن تحسين الأداء من خلال متابعة الطلبات المعلقة وتجديد المخزون';
    }
  }
}
