import 'package:flutter/material.dart';
import 'package:get/get.dart';

// Mock notification model
class NotificationModel {
  final String id;
  final String title;
  final String message;
  final String type;
  final DateTime createdAt;
  final bool isRead;
  final Map<String, dynamic>? data;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.createdAt,
    this.isRead = false,
    this.data,
  });

  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    DateTime? createdAt,
    bool? isRead,
    Map<String, dynamic>? data,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
    );
  }
}

class NotificationsController extends GetxController {
  // State
  final _notifications = <NotificationModel>[].obs;
  final _isLoading = false.obs;
  final _isLoadingMore = false.obs;
  final _hasMore = true.obs;
  final _error = Rx<String?>(null);
  final _selectedFilter = 'all'.obs;
  final _currentPage = 1.obs;

  // Getters
  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading.value;
  bool get isLoadingMore => _isLoadingMore.value;
  bool get hasMore => _hasMore.value;
  String? get error => _error.value;
  String get selectedFilter => _selectedFilter.value;

  // Filter options
  final filterOptions = [
    {'key': 'all', 'label': 'الكل'},
    {'key': 'order', 'label': 'الطلبات'},
    {'key': 'inventory', 'label': 'المخزون'},
    {'key': 'system', 'label': 'النظام'},
    {'key': 'unread', 'label': 'غير مقروء'},
  ];

  // Computed properties
  List<NotificationModel> get filteredNotifications {
    switch (_selectedFilter.value) {
      case 'unread':
        return _notifications.where((n) => !n.isRead).toList();
      case 'order':
        return _notifications.where((n) => n.type == 'order').toList();
      case 'inventory':
        return _notifications.where((n) => n.type == 'inventory').toList();
      case 'system':
        return _notifications.where((n) => n.type == 'system').toList();
      default:
        return _notifications;
    }
  }

  bool get hasUnreadNotifications {
    return _notifications.any((n) => !n.isRead);
  }

  int get unreadCount {
    return _notifications.where((n) => !n.isRead).length;
  }

  @override
  void onInit() {
    super.onInit();
    loadNotifications();
  }

  // Filter methods
  void setFilter(String filter) {
    if (_selectedFilter.value != filter) {
      _selectedFilter.value = filter;
    }
  }

  // Data loading methods
  Future<void> loadNotifications() async {
    try {
      _isLoading.value = true;
      _error.value = null;
      _currentPage.value = 1;

      // Simulate API call with mock data
      await Future.delayed(const Duration(seconds: 1));

      final mockNotifications = _generateMockNotifications();
      _notifications.assignAll(mockNotifications);
      _hasMore.value = mockNotifications.length >= 20;
    } catch (e) {
      _error.value = 'فشل في تحميل الإشعارات: ${e.toString()}';
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> loadMoreNotifications() async {
    if (_isLoadingMore.value || !_hasMore.value) return;

    try {
      _isLoadingMore.value = true;
      _currentPage.value++;

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final moreNotifications = _generateMockNotifications(
        startIndex: _notifications.length,
        count: 10,
      );

      _notifications.addAll(moreNotifications);
      _hasMore.value = moreNotifications.length >= 10;
    } catch (e) {
      _error.value = 'فشل في تحميل المزيد من الإشعارات: ${e.toString()}';
    } finally {
      _isLoadingMore.value = false;
    }
  }

  Future<void> refreshNotifications() async {
    _error.value = null;
    await loadNotifications();
  }

  // Notification actions
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1 && !_notifications[index].isRead) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      _notifications.refresh();
    }
  }

  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      if (!_notifications[i].isRead) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
      }
    }
    _notifications.refresh();

    Get.snackbar(
      'تم',
      'تم تعيين جميع الإشعارات كمقروءة',
      snackPosition: SnackPosition.bottom,
    );
  }

  void dismissNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);

    Get.snackbar(
      'تم الحذف',
      'تم حذف الإشعار',
      snackPosition: SnackPosition.bottom,
    );
  }

  // Utility methods
  String getFilterLabel(String key) {
    final option = filterOptions.firstWhere(
      (option) => option['key'] == key,
      orElse: () => {'label': 'غير معروف'},
    );
    return option['label'] as String;
  }

  IconData getNotificationIcon(String type) {
    switch (type) {
      case 'order':
        return Icons.receipt_long;
      case 'inventory':
        return Icons.inventory;
      case 'system':
        return Icons.settings;
      default:
        return Icons.notifications;
    }
  }

  Color getNotificationColor(String type) {
    switch (type) {
      case 'order':
        return Colors.blue;
      case 'inventory':
        return Colors.orange;
      case 'system':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  // Mock data generation
  List<NotificationModel> _generateMockNotifications({
    int startIndex = 0,
    int count = 20,
  }) {
    final notifications = <NotificationModel>[];
    final now = DateTime.now();

    final mockData = [
      {
        'type': 'order',
        'title': 'طلب جديد',
        'message': 'تم استلام طلب جديد من متجر الأمل',
        'data': {'orderId': '123', 'storeName': 'متجر الأمل'},
      },
      {
        'type': 'inventory',
        'title': 'مخزون منخفض',
        'message': 'مخزون منتج الأرز الأبيض أصبح أقل من 10 قطع',
        'data': {
          'productId': '456',
          'productName': 'الأرز الأبيض',
          'quantity': 8,
        },
      },
      {
        'type': 'order',
        'title': 'تم تأكيد الطلب',
        'message': 'تم تأكيد الطلب رقم 124 من متجر النور',
        'data': {'orderId': '124', 'storeName': 'متجر النور'},
      },
      {
        'type': 'system',
        'title': 'تحديث النظام',
        'message': 'تم تحديث النظام إلى الإصدار الجديد',
        'data': {'version': '2.1.0'},
      },
      {
        'type': 'inventory',
        'title': 'نفد المخزون',
        'message': 'نفد مخزون منتج السكر الأبيض',
        'data': {'productId': '789', 'productName': 'السكر الأبيض'},
      },
    ];

    for (int i = 0; i < count; i++) {
      final mockItem = mockData[i % mockData.length];
      notifications.add(
        NotificationModel(
          id: '${startIndex + i + 1}',
          title: mockItem['title'] as String,
          message: mockItem['message'] as String,
          type: mockItem['type'] as String,
          createdAt: now.subtract(Duration(hours: i * 2)),
          isRead: i % 3 == 0, // Some notifications are read
          data: mockItem['data'] as Map<String, dynamic>?,
        ),
      );
    }

    return notifications;
  }
}
