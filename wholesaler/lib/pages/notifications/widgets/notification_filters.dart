import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import '../notifications_controller.dart';

class NotificationFilters extends StatelessWidget {
  const NotificationFilters({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<NotificationsController>();

    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: controller.filterOptions.length,
        itemBuilder: (context, index) {
          final option = controller.filterOptions[index];
          final key = option['key'] as String;
          final label = option['label'] as String;
          
          return Obx(() {
            final isSelected = controller.selectedFilter == key;
            
            // Add unread count for unread filter
            String displayLabel = label;
            if (key == 'unread' && controller.unreadCount > 0) {
              displayLabel = '$label (${controller.unreadCount})';
            }
            
            return Padding(
              padding: const EdgeInsets.only(left: 8),
              child: FilterChip(
                label: Text(
                  displayLabel,
                  style: TextStyle(
                    color: isSelected ? Colors.white : AppColors.primary,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    fontSize: 13,
                  ),
                ),
                selected: isSelected,
                onSelected: (_) => controller.setFilter(key),
                backgroundColor: Colors.white,
                selectedColor: AppColors.primary,
                checkmarkColor: Colors.white,
                side: BorderSide(
                  color: isSelected ? AppColors.primary : Colors.grey[300]!,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
                showCheckmark: false,
              ),
            );
          });
        },
      ),
    );
  }
}
