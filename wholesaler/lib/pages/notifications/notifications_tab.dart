import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'notifications_controller.dart';
import 'widgets/notification_card.dart';
import 'widgets/notification_filters.dart';

class NotificationsTab extends StatelessWidget {
  const NotificationsTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put<NotificationsController>(NotificationsController());

    return Scaffold(
      backgroundColor: AppColors.scaffoldWithBoxBackground,
      body: Column(
        children: [
          // Header with filters
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(AppDefaults.padding),
            child: Column(
              children: [
                // Title and mark all read
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'الإشعارات',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Obx(() => controller.hasUnreadNotifications
                        ? TextButton(
                            onPressed: controller.markAllAsRead,
                            child: const Text('تعيين الكل كمقروء'),
                          )
                        : const SizedBox.shrink()),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Notification filters
                const NotificationFilters(),
              ],
            ),
          ),
          
          // Notifications list
          Expanded(
            child: Obx(() {
              if (controller.isLoading && controller.notifications.isEmpty) {
                return const Center(
                  child: CircularProgressIndicator(
                    color: AppColors.primary,
                  ),
                );
              }

              if (controller.error != null) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        controller.error!,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: controller.refreshNotifications,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                );
              }

              if (controller.filteredNotifications.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.notifications_none,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        controller.notifications.isEmpty 
                            ? 'لا توجد إشعارات'
                            : 'لا توجد إشعارات بالفلتر المحدد',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        controller.notifications.isEmpty
                            ? 'ستظهر الإشعارات هنا عند وصولها'
                            : 'جرب تغيير الفلتر لعرض إشعارات أخرى',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[500],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: controller.refreshNotifications,
                color: AppColors.primary,
                child: ListView.builder(
                  padding: const EdgeInsets.all(AppDefaults.padding),
                  itemCount: controller.filteredNotifications.length + 
                             (controller.hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == controller.filteredNotifications.length) {
                      // Load more indicator
                      if (controller.isLoadingMore) {
                        return const Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(
                            child: CircularProgressIndicator(
                              color: AppColors.primary,
                            ),
                          ),
                        );
                      } else {
                        // Load more button
                        return Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Center(
                            child: ElevatedButton(
                              onPressed: controller.loadMoreNotifications,
                              child: const Text('تحميل المزيد'),
                            ),
                          ),
                        );
                      }
                    }

                    final notification = controller.filteredNotifications[index];
                    return NotificationCard(
                      notification: notification,
                      onTap: () => controller.markAsRead(notification.id),
                      onDismiss: () => controller.dismissNotification(notification.id),
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}
