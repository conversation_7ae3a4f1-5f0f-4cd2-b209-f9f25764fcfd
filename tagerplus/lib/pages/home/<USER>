import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:tagerplus/components/network_image.dart';
import 'package:tagerplus/components/product_tile_square.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/constants/app_icons.dart';
import 'package:tagerplus/pages/cart/carts.dart';
import 'package:tagerplus/pages/home/<USER>';
import 'package:tagerplus/pages/orders/list/list.dart';
import 'package:tagerplus/pages/products/details/product_details.dart';
import 'package:tagerplus/pages/wholesaler/wholesaler_details.dart';
import 'package:tagerplus/services/auth.dart';
import 'package:tagerplus/pages/profile/profile_page.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.put<HomeController>(HomeController());
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        bottomNavigationBar: Obx(
          () => BottomAppBar(
            shape: const CircularNotchedRectangle(),
            notchMargin: AppDefaults.margin,
            color: AppColors.scaffoldBackground,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                BottomAppBarItem(
                  name: 'الرئيسية',
                  iconLocation: AppIcons.home,
                  isActive: c.currentIndex == 0,
                  onTap: () => c.currentIndex = 0,
                ),
                BottomAppBarItem(
                  name: 'السلة',
                  iconLocation: AppIcons.shoppingCart,
                  isActive: c.currentIndex == 1,
                  onTap: () => c.currentIndex = 1,
                ),
                BottomAppBarItem(
                  name: 'المحفوظات',
                  iconLocation: AppIcons.save,
                  isActive: c.currentIndex == 3,
                  onTap: () => c.currentIndex = 3,
                ),
                BottomAppBarItem(
                  name: 'الطلبات',
                  iconLocation: AppIcons.orderProcessing,
                  isActive: c.currentIndex == 4,
                  onTap: () => c.currentIndex = 4,
                ),
              ],
            ),
          ),
        ),
        appBar: AppBar(
          title: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(AppIcons.logoCropped, height: 32),
              const SizedBox(width: 11),
              Text(
                "تاجر بلس",
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 8, top: 4, bottom: 4),
              child: ElevatedButton(
                onPressed: () {
                  Get.to(() => const ProfilePage());
                },
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.all(0),
                  backgroundColor: const Color(0xFFF2F6F3),
                  shape: const CircleBorder(),
                ),
                child: SvgPicture.asset(
                  AppIcons.profile,
                  fit: BoxFit.fitHeight,
                ),
              ),
            ),
          ],
        ),
        body: Obx(
          () => IndexedStack(
            index: c.currentIndex,
            children: [
              // Index 0: Home content (existing)
              ListView(
                children: [
                  StoreSelector(),
                  const SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: TextFormField(
                      decoration: InputDecoration(
                        hintText: 'ابحث عن منتج',
                        prefixIcon: Padding(
                          padding: const EdgeInsets.all(AppDefaults.padding),
                          child: SvgPicture.asset(
                            AppIcons.search,
                            colorFilter: const ColorFilter.mode(
                              AppColors.primary,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                        prefixIconConstraints: const BoxConstraints(),
                        contentPadding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                      textInputAction: TextInputAction.search,
                      autofocus: true,
                      onChanged: c.onSearchChanged,
                      onFieldSubmitted: (v) {},
                    ),
                  ),
                  // Show search results or regular content
                  Obx(() {
                    if (c.isSearching) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (c.searchResults.isEmpty)
                            const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Center(child: Text('لا توجد نتائج')),
                            )
                          else
                            Wrap(
                              runSpacing: 16,
                              children: [
                                for (final product in c.searchResults)
                                  ProductTileSquare(
                                    title: product.name,
                                    description: product.description,
                                    price: product.items?.isNotEmpty == true
                                        ? product.items!.first.basePrice
                                        : null,
                                    imageUrl: product.imageUrl,
                                    onTap: () {
                                      Get.to(
                                        () =>
                                            ProductDetailsPage(id: product.id),
                                      );
                                    },
                                  ),
                              ],
                            ),
                        ],
                      );
                    }

                    return Column(
                      children: [
                        AdSpace(),
                        Wrap(
                          children: [
                            for (final wholesaler in c.wholesalers)
                              Container(
                                margin: const EdgeInsets.all(8),
                                width: MediaQuery.of(context).size.width * 0.45,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.grey.withValues(alpha: 0.25),
                                  ),
                                ),
                                child: InkWell(
                                  onTap: () => Get.to(
                                    () => WholesalerDetailsPage(
                                      id: wholesaler.id,
                                    ),
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                  child: Padding(
                                    padding: const EdgeInsets.all(12),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        AspectRatio(
                                          aspectRatio: 1,
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                            child: wholesaler.logo != null
                                                ? Image.network(
                                                    wholesaler.logo!,
                                                    fit: BoxFit.cover,
                                                  )
                                                : Container(
                                                    color: Colors.grey[200],
                                                    child: const Icon(
                                                      Icons.store,
                                                      color: Colors.grey,
                                                      size: 40,
                                                    ),
                                                  ),
                                          ),
                                        ),
                                        const SizedBox(height: 12),
                                        Text(
                                          wholesaler.title,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          '${c.getMinChargeForWholesaler(wholesaler.id)?.minCharge ?? 0} جنيه مصري',
                                          style: TextStyle(
                                            color: Colors.green[700],
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          'بحد ادنى للمنتجات: ${c.getMinChargeForWholesaler(wholesaler.id)?.minCharge ?? 0} جنيه مصري',
                                          style: TextStyle(
                                            fontSize: 11,
                                            color: Colors.grey[600],
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    );
                  }),

                  const SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      'منتجات مميزة',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineSmall?.copyWith(color: Colors.black),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Obx(
                    () => Wrap(
                      runSpacing: 16,
                      children: [
                        for (final product in c.products)
                          Builder(
                            builder: (context) {
                              final items = product.items ?? [];
                              items.sort(
                                (a, b) => a.basePrice.compareTo(b.basePrice),
                              );
                              return ProductTileSquare(
                                title: product.title,
                                description: product.description,
                                imageUrl: product.imageUrl,
                                price: items.first.basePrice,
                                insteadOfPrice: items.length > 1
                                    ? items[1].basePrice
                                    : null,
                                productId: product.id,
                                onTap: () => Get.to(
                                  () => ProductDetailsPage(id: product.id),
                                ),
                              );
                            },
                          ),
                      ],
                    ),
                  ),
                ],
              ),
              // Index 1: Carts tab
              const CartsPage(),
              // Index 2: Placeholder (search or other)
              const SizedBox.shrink(),
              // Index 3: Saved placeholder
              const Center(child: Text('المحفوظات')),
              // Index 4: Profile placeholder
              const OrdersListPage(),
            ],
          ),
        ),
      ),
    );
  }
}

class AdSpace extends StatefulWidget {
  const AdSpace({super.key});

  @override
  State<AdSpace> createState() => _AdSpaceState();
}

class _AdSpaceState extends State<AdSpace> {
  final PageController _pageController = PageController();
  Timer? _timer;
  int _currentPage = 0;
  late final HomeController _c;

  @override
  void initState() {
    super.initState();
    _c = Get.find<HomeController>();
    _startAutoScroll();
  }

  void _startAutoScroll() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 5), (_) {
      final total = _c.banners.length;
      if (total == 0 || !_pageController.hasClients) return;
      _currentPage = (_currentPage + 1) % total;
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: Obx(() {
            final banners = _c.banners;
            if (banners.isEmpty) {
              return const SizedBox.shrink();
            }
            if (_currentPage >= banners.length) {
              _currentPage = 0;
            }
            return Stack(
              fit: StackFit.expand,
              children: [
                PageView.builder(
                  controller: _pageController,
                  itemCount: banners.length,
                  onPageChanged: (i) => setState(() => _currentPage = i),
                  physics: const BouncingScrollPhysics(),
                  itemBuilder: (ctx, i) =>
                      NetworkImageWithLoader(banners[i], fit: BoxFit.contain),
                ),
                Positioned(
                  bottom: 8,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(banners.length, (i) {
                      final isActive = i == _currentPage;
                      return AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        margin: const EdgeInsets.symmetric(horizontal: 3),
                        width: isActive ? 10 : 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: isActive ? Colors.white : Colors.white70,
                          borderRadius: BorderRadius.circular(3),
                        ),
                      );
                    }),
                  ),
                ),
              ],
            );
          }),
        ),
      ),
    );
  }
}

class StoreSelector extends StatelessWidget {
  const StoreSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDefaults.padding,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.store, color: AppColors.primary, size: 20),
        ),
        title: Text(
          'المتجر الحالي',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AuthService.to.currentStore.value?.name ?? '',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            if (AuthService.to.stores.isNotEmpty)
              Text(
                AuthService.to.stores.first.address,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[500]),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (AuthService.to.isClosed)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else
              const Icon(
                Icons.edit_location_alt,
                color: AppColors.primary,
                size: 20,
              ),
            const SizedBox(width: 4),
            Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16),
          ],
        ),
        // onTap: _storeService.isLoading ? null : _showStoreSelectionDialog,
      ),
    );
  }
}

class BottomAppBarItem extends StatelessWidget {
  const BottomAppBarItem({
    super.key,
    required this.iconLocation,
    required this.name,
    required this.isActive,
    required this.onTap,
  });

  final String iconLocation;
  final String name;
  final bool isActive;
  final void Function() onTap;

  @override
  Widget build(BuildContext context) {
    return Flexible(
      flex: 1,
      child: InkWell(
        onTap: onTap,
        child: SizedBox.expand(
          child: Column(
            // mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                iconLocation,
                colorFilter: ColorFilter.mode(
                  isActive ? AppColors.primary : AppColors.placeholder,
                  BlendMode.srcIn,
                ),
              ),
              Text(
                name,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: isActive ? AppColors.primary : AppColors.placeholder,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
