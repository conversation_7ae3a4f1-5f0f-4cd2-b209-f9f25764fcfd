import 'dart:convert';
import 'dart:core';

import 'package:get/get.dart' hide Response;
import 'package:flutter/foundation.dart';
import 'package:tagerplus/core/api/api.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/storage.dart';

class AuthService extends GetxService {
  static AuthService get to => Get.find();

  final token = ''.obs;
  final user = Rx<UserResponse?>(null);
  final stores = List<StoreSchema>.empty(growable: true).obs;
  final currentStore = Rx<StoreSchema?>(null);
  final wholesalerId = ''.obs;

  bool get isLoggedIn => token.value.isNotEmpty;
  bool get hasStores => stores.isNotEmpty;

  Future<AuthService> init() async {
    // get token and validate it to see if it's valid
    token.value = LocalStorageService.to.token ?? '';
    if (isLoggedIn) {
      ApiService.to.apiClient.setAuthorizationHeader(token.value);
      await getCurrentUser();
      await loadStores();
      wholesalerId.value = LocalStorageService.to.wholesalerId ?? '';
    }
    return this;
  }

  Future<void> login(String token) async {
    this.token.value = token;
    LocalStorageService.to.token = token;
    ApiService.to.apiClient.setAuthorizationHeader(token);
    await getCurrentUser();
    await loadStores();
  }

  Future<void> logout() async {
    await LocalStorageService.to.clear();
    token.value = '';
    ApiService.to.apiClient.removeAuthorizationHeader();
    user.value = null;
    LocalStorageService.to.clear();
  }

  Future<UserResponse?> getCurrentUser() async {
    if (!isLoggedIn) return null;
    final res = await ApiService.to.apiClient.auth.getCurrentUser();
    user.value = res.data;
    LocalStorageService.to.user = jsonEncode(res.data!.toJson());
    return res.data;
  }

  /// Login with phone and password
  Future<LoginResponse> loginWithCredentials({
    required String phone,
    required String password,
  }) async {
    try {
      final response = await ApiService.to.apiClient.auth.signin(
        LoginRequest(phone: phone, password: password),
      );

      // Set token and user data
      await login(response.data!.token);

      return response.data!;
    } on Exception catch (e) {
      if (kDebugMode) {
        print('Login error: $e');
      }
      rethrow;
    }
  }

  /// Sign up new user
  Future<Response<RegisterResponse>> signupUser({
    required String firstName,
    required String lastName,
    required String phone,
    required String password,
    required String confirmPassword,
    String? email,
  }) async {
    try {
      final response = await ApiService.to.apiClient.auth.signup(
        RegisterRequest(
          name: '$firstName $lastName',
          phone: phone,
          password: password,
          email: email,
        ),
      );

      // Set token and user data
      await login(response.data!.token);

      return response;
    } on Exception catch (e) {
      if (kDebugMode) {
        print('Signup error: $e');
      }
      rethrow;
    }
  }

  /// Load user stores
  Future<void> loadStores() async {
    if (!isLoggedIn) return;

    try {
      final response = await ApiService.to.apiClient.stores.listMyStores();
      final userStores = response.data!.data;
      stores.value = List<StoreSchema>.from(userStores);

      // Set current store if available
      if (userStores.isNotEmpty) {
        currentStore.value = userStores.first;
        LocalStorageService.to.currentStore = jsonEncode(
          userStores.first.toJson(),
        );
      }

      // Save stores to local storage
      LocalStorageService.to.stores = jsonEncode(
        userStores.map((store) => store.toJson()).toList(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error loading stores: $e');
      }
    }
  }

  /// Create a new store
  Future<StoreSchema> createStore({
    required String name,
    String? description,
    required String address,
    int? cityId,
    int? stateId,
    int? countryId,
  }) async {
    try {
      final newStore = await ApiService.to.apiClient.stores.createStore(
        StoreIn(
          name: name,
          description: description ?? '',
          address: address,
          cityId: cityId,
          stateId: stateId,
          countryId: countryId,
          ownerId: user.value!.id,
        ),
      );

      // Add to stores list
      stores.add(newStore.data!);

      // Set as current store if it's the first one
      if (currentStore.value == null) {
        currentStore.value = newStore.data;
        LocalStorageService.to.currentStore = jsonEncode(
          newStore.data!.toJson(),
        );
      }

      // Update local storage
      LocalStorageService.to.stores = jsonEncode(
        stores.map((store) => store.toJson()).toList(),
      );

      return newStore.data!;
    } on Exception catch (e) {
      if (kDebugMode) {
        print('Create store error: $e');
      }
      rethrow;
    }
  }

  /// Set current store
  void setCurrentStore(StoreSchema store) {
    currentStore.value = store;
    LocalStorageService.to.currentStore = jsonEncode(store.toJson());
  }
}
