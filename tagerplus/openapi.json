{"openapi": "3.1.0", "info": {"title": "NinjaAPI", "version": "1.0.0", "description": ""}, "paths": {"/api/": {"get": {"operationId": "core_api_api_root", "summary": "Api Root", "parameters": [], "responses": {"200": {"description": "OK"}}, "description": "API root endpoint", "tags": ["core"]}}, "/api/signin": {"post": {"operationId": "core_api_signin", "summary": "Signin", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}}, "description": "User authentication endpoint\nReturns JWT token and user details including wholesaler_id if applicable", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}}}, "/api/signup": {"post": {"operationId": "core_api_signup", "summary": "Signup", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterResponse"}}}}}, "description": "User registration endpoint\nCreates user account with phone verification set to False", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}, "required": true}}}, "/api/me": {"get": {"operationId": "core_api_get_current_user", "summary": "Get Current User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "description": "Get the current authenticated user's data\nRequires authentication via JWT token", "tags": ["auth"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "core_api_update_current_user", "summary": "Update Current User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "description": "Update the current authenticated user's data\nRequires authentication via JWT token", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/home/<USER>": {"get": {"operationId": "core_api_get_home_products", "summary": "Get Home Products", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "region_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Region Id"}, "required": false}, {"in": "query", "name": "wholesaler_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "category_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "required": false}, {"in": "query", "name": "company_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedProductResponse"}}}}}, "description": "Get products for home page with pagination and filtering support.\n\nQuery Parameters:\n- page: Page number (default: 1)\n- page_size: Number of items per page (default: 20, max: 100)\n- region_id: Filter by region ID (includes parent regions)\n- wholesaler_id: Filter by specific wholesaler ID\n- search: Search term for product name/title/description\n- category_id: Filter by category ID\n- company_id: Filter by company ID", "tags": ["home"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/users/": {"get": {"operationId": "accounts_api_custom_user_list_users", "summary": "List Users", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "is_active", "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "required": false}, {"in": "query", "name": "phone_verified", "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Phone Verified"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedCustomUserResponse"}}}}}, "description": "List all users with pagination and filtering.\nRequires authentication.", "tags": ["users"], "security": [{"AuthBearerMiddleware": []}]}, "post": {"operationId": "accounts_api_custom_user_create_user", "summary": "Create User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomUserOut"}}}}}, "description": "Create a new user.\nRequires authentication.", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomUserIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/users/{user_id}": {"get": {"operationId": "accounts_api_custom_user_get_user", "summary": "Get User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomUserOut"}}}}}, "description": "Get a specific user by ID.\nRequires authentication.", "tags": ["users"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "accounts_api_custom_user_update_user", "summary": "Update User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomUserOut"}}}}}, "description": "Update a user.\nRequires authentication.", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomUserUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "accounts_api_custom_user_delete_user", "summary": "Delete User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Soft delete a user.\nRequires authentication.", "tags": ["users"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/products/": {"get": {"operationId": "products_api_product_list_products", "summary": "List Products", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "company_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "required": false}, {"in": "query", "name": "category_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "required": false}, {"in": "query", "name": "unit", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Unit"}, "required": false}, {"in": "query", "name": "region_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Region Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedProductResponse"}}}}}, "description": "List all products with pagination and filtering.\nRequires authentication.", "tags": ["products"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/products/{product_id}": {"get": {"operationId": "products_api_product_get_product", "summary": "Get Product", "parameters": [{"in": "path", "name": "product_id", "schema": {"title": "Product Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductSchema"}}}}}, "description": "Get a specific product by ID.\nRequires authentication.", "tags": ["products"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/": {"get": {"operationId": "products_api_region_list_regions", "summary": "List Regions", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "type", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}, "required": false}, {"in": "query", "name": "parent_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Parent Id"}, "required": false}, {"in": "query", "name": "is_active", "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_RegionSchema_"}}}}}, "description": "List all regions with pagination and filtering.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/countries": {"get": {"operationId": "products_api_region_get_countries", "summary": "Get Countries", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionSchema"}, "title": "Response", "type": "array"}}}}}, "description": "List all countries.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/states": {"get": {"operationId": "products_api_region_get_states", "summary": "Get States", "parameters": [{"in": "query", "name": "country_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Country Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionSchema"}, "title": "Response", "type": "array"}}}}}, "description": "List all states/provinces, optionally filtered by country.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/cities": {"get": {"operationId": "products_api_region_get_cities", "summary": "Get Cities", "parameters": [{"in": "query", "name": "state_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "State Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionSchema"}, "title": "Response", "type": "array"}}}}}, "description": "List all cities, optionally filtered by state.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/{region_id}": {"get": {"operationId": "products_api_region_get_region", "summary": "Get Region", "parameters": [{"in": "path", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegionSchema"}}}}}, "description": "Get a specific region by ID.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/stores/": {"get": {"operationId": "stores_api_store_list_stores", "summary": "List Stores", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "owner_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Owner Id"}, "required": false}, {"in": "query", "name": "city_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "City Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_StoreSchema_"}}}}}, "description": "List all stores with pagination and filtering.\nRequires authentication.", "tags": ["stores"], "security": [{"AuthBearerMiddleware": []}]}, "post": {"operationId": "stores_api_store_create_store", "summary": "Create Store", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreSchema"}}}}}, "description": "Create a new store.\nRequires authentication.", "tags": ["stores"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/stores/my": {"get": {"operationId": "stores_api_store_list_my_stores", "summary": "List My Stores", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_StoreSchema_"}}}}}, "description": "List current user's stores with pagination.\nRequires authentication.", "tags": ["stores"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/stores/{store_id}": {"get": {"operationId": "stores_api_store_get_store", "summary": "Get Store", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreSchema"}}}}}, "description": "Get a specific store by ID.\nRequires authentication.", "tags": ["stores"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "stores_api_store_update_store", "summary": "Update Store", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreSchema"}}}}}, "description": "Update a store.\nRequires authentication.", "tags": ["stores"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "stores_api_store_delete_store", "summary": "Delete Store", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Soft delete a store.\nRequires authentication.", "tags": ["stores"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/orders/": {"get": {"operationId": "stores_api_order_list_orders", "summary": "List Orders", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "status", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "required": false}, {"in": "query", "name": "store_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Store Id"}, "required": false}, {"in": "query", "name": "wholesaler_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedOrderResponse"}}}}}, "description": "List all orders with pagination and filtering.\nRequires authentication.", "tags": ["orders"], "security": [{"AuthBearerMiddleware": []}]}, "post": {"operationId": "stores_api_order_create_order", "summary": "Create Order", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOut"}}}}}, "description": "Create a new order.\nRequires authentication.", "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/orders/{order_id}": {"get": {"operationId": "stores_api_order_get_order", "summary": "Get Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOut"}}}}}, "description": "Get a specific order by ID.\nRequires authentication.", "tags": ["orders"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/": {"get": {"operationId": "wholesalers_api_wholesaler_list_wholesalers", "summary": "List Wholesalers", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "region_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Region Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_WholesalerSchema_"}}}}}, "description": "List all wholesalers with pagination and filtering.\nRequires authentication.", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/{wholesaler_id}": {"get": {"operationId": "wholesalers_api_wholesaler_get_wholesaler", "summary": "Get Wholesaler", "parameters": [{"in": "path", "name": "wholesaler_id", "schema": {"title": "Wholesaler Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerSchema"}}}}}, "description": "Get a specific wholesaler by ID.\nRequires authentication.", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/min-charges/": {"get": {"operationId": "wholesalers_api_min_charge_list_min_charges", "summary": "List Min Charges", "parameters": [{"in": "query", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}, {"in": "query", "name": "wholesaler_id", "schema": {"title": "Wholesaler Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionMinChargeSchema"}, "title": "Response", "type": "array"}}}}}, "description": "List all min charges.\nRequires authentication.", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/items/": {"get": {"operationId": "wholesalers_api_items_list_items", "summary": "List Items", "parameters": [{"in": "query", "name": "wholesaler_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}, "required": false}, {"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 100, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "category_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "required": false}, {"in": "query", "name": "product_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Product Id"}, "required": false}, {"in": "query", "name": "region_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Region Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_ItemSchema_"}}}}}, "description": "List items for a specific wholesaler including product and category data.\nExcludes items with zero inventory or zero price.", "tags": ["items"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/items/by_id": {"get": {"operationId": "wholesalers_api_items_get_items_by_ids", "summary": "Get Items By Ids", "parameters": [{"in": "query", "name": "ids", "schema": {"title": "Ids", "type": "string"}, "required": true}, {"in": "query", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ItemSchema"}, "title": "Response", "type": "array"}}}}}, "description": "Get a list of items by their IDs.\nIDs should be provided as a comma-separated string.\ne.g., /api/items/by_ids?ids=1,2,3", "tags": ["items"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/gomla/dashboard": {"get": {"operationId": "wholesalers_api_gomla_api_get_dashboard", "summary": "Get Dashboard", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardOut"}}}}}, "description": "Get wholesaler dashboard data including statistics, recent orders, and sales data.\nRequires wholesaler authentication.", "tags": ["gomla"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/gomla/orders": {"get": {"operationId": "wholesalers_api_gomla_api_list_orders", "summary": "List Orders", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "status", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "date_from", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Date From"}, "required": false}, {"in": "query", "name": "date_to", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Date To"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedOrdersOut"}}}}}, "description": "List all orders for the wholesaler with filtering and pagination.\nRequires wholesaler authentication.", "tags": ["gomla"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/gomla/orders/{order_id}": {"get": {"operationId": "wholesalers_api_gomla_api_get_order_detail", "summary": "Get Order Detail", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDetailOut"}}}}}, "description": "Get detailed information about a specific order.\nRequires wholesaler authentication.", "tags": ["gomla"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/gomla/orders/{order_id}/status": {"put": {"operationId": "wholesalers_api_gomla_api_update_order_status", "summary": "Update Order Status", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Update the status of an order.\nRequires wholesaler authentication.", "tags": ["gomla"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderStatusIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/gomla/orders/{order_id}/items/{item_id}": {"delete": {"operationId": "wholesalers_api_gomla_api_remove_order_item", "summary": "Remove Order Item", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}, {"in": "path", "name": "item_id", "schema": {"title": "Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Remove an item from an order and restore inventory.\nRequires wholesaler authentication.", "tags": ["gomla"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveOrderItemIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/gomla/orders/{order_id}/items/{item_id}/quantity": {"put": {"operationId": "wholesalers_api_gomla_api_decrease_order_item_quantity", "summary": "Decrease Order Item Quantity", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}, {"in": "path", "name": "item_id", "schema": {"title": "Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Decrease the quantity of an item in an order and restore the difference to inventory.\nRequires wholesaler authentication.", "tags": ["gomla"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DecreaseOrderItemQuantityIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/gomla/items": {"get": {"operationId": "wholesalers_api_gomla_api_list_items", "summary": "List Items", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "company", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company"}, "required": false}, {"in": "query", "name": "category", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category"}, "required": false}, {"in": "query", "name": "stock", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stock"}, "required": false}, {"in": "query", "name": "sort", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "-created_at", "title": "Sort"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedItemsOut"}}}}}, "description": "List all items/inventory for the wholesaler with filtering and pagination.\nRequires wholesaler authentication.", "tags": ["gomla"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/gomla/items/{item_id}": {"get": {"operationId": "wholesalers_api_gomla_api_get_item_detail", "summary": "Get Item Detail", "parameters": [{"in": "path", "name": "item_id", "schema": {"title": "Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemOut"}}}}}, "description": "Get detailed information about a specific item.\nRequires wholesaler authentication.", "tags": ["gomla"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "wholesalers_api_gomla_api_update_item", "summary": "Update Item", "parameters": [{"in": "path", "name": "item_id", "schema": {"title": "Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemOut"}}}}}, "description": "Update an existing item.\nRequires wholesaler authentication.", "tags": ["gomla"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemUpdateIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "wholesalers_api_gomla_api_delete_item", "summary": "Delete Item", "parameters": [{"in": "path", "name": "item_id", "schema": {"title": "Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Delete an item (soft delete).\nRequires wholesaler authentication.", "tags": ["gomla"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/gomla/products/available": {"get": {"operationId": "wholesalers_api_gomla_api_list_available_products", "summary": "List Available Products", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "company", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company"}, "required": false}, {"in": "query", "name": "category", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedProductsForBulkOut"}}}}}, "description": "List products available for adding to inventory (not already in wholesaler's inventory).\nRequires wholesaler authentication.", "tags": ["gomla"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/gomla/items/bulk-add": {"post": {"operationId": "wholesalers_api_gomla_api_bulk_add_items", "summary": "Bulk Add Items", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkAddResultOut"}}}}}, "description": "Bulk add items with default values.\nRequires wholesaler authentication.", "tags": ["gomla"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkAddItemsIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/gomla/items/bulk-update": {"put": {"operationId": "wholesalers_api_gomla_api_bulk_update_items", "summary": "Bulk Update Items", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateResultOut"}}}}}, "description": "Bulk update multiple items.\nRequires wholesaler authentication.", "tags": ["gomla"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateItemsIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/banners": {"get": {"operationId": "internals_apis_get_banners", "summary": "Get Banners", "parameters": [{"in": "query", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/Banner"}, "title": "Response", "type": "array"}}}}}, "tags": ["index"]}}}, "components": {"schemas": {"LoginResponse": {"properties": {"success": {"title": "Success", "type": "boolean"}, "token": {"title": "Token", "type": "string"}, "user_id": {"title": "User Id", "type": "integer"}, "phone": {"title": "Phone", "type": "string"}, "is_phone_verified": {"title": "Is Phone Verified", "type": "boolean"}, "wholesaler_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}}, "required": ["success", "token", "user_id", "phone", "is_phone_verified"], "title": "LoginResponse", "type": "object"}, "LoginRequest": {"properties": {"phone": {"title": "Phone", "type": "string"}, "password": {"title": "Password", "type": "string"}}, "required": ["phone", "password"], "title": "LoginRequest", "type": "object"}, "RegisterResponse": {"properties": {"success": {"title": "Success", "type": "boolean"}, "user_id": {"title": "User Id", "type": "integer"}, "phone": {"title": "Phone", "type": "string"}, "message": {"title": "Message", "type": "string"}, "token": {"title": "Token", "type": "string"}}, "required": ["success", "user_id", "phone", "message", "token"], "title": "RegisterResponse", "type": "object"}, "RegisterRequest": {"properties": {"name": {"title": "Name", "type": "string"}, "password": {"title": "Password", "type": "string"}, "phone": {"title": "Phone", "type": "string"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}}, "required": ["name", "password", "phone"], "title": "RegisterRequest", "type": "object"}, "UserResponse": {"description": "Response schema for user data", "properties": {"id": {"title": "Id", "type": "integer"}, "username": {"title": "Username", "type": "string"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"title": "Phone", "type": "string"}, "phone_verified": {"title": "Phone Verified", "type": "boolean"}, "first_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Name"}, "is_active": {"title": "Is Active", "type": "boolean"}, "date_joined": {"format": "date-time", "title": "Date Joined", "type": "string"}, "wholesaler_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}}, "required": ["id", "username", "phone", "phone_verified", "is_active", "date_joined"], "title": "UserResponse", "type": "object"}, "UserUpdateRequest": {"description": "Request schema for updating user data", "properties": {"first_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Name"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}}, "title": "UserUpdateRequest", "type": "object"}, "CategoryOut": {"description": "Category information schema", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}}, "required": ["id", "name", "title"], "title": "CategoryOut", "type": "object"}, "CompanyOut": {"description": "Company information schema", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}}, "required": ["id", "name", "title"], "title": "CompanyOut", "type": "object"}, "PaginatedProductResponse": {"description": "Paginated response for products", "properties": {"products": {"items": {"$ref": "#/components/schemas/ProductSchema"}, "title": "Products", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["products", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedProductResponse", "type": "object"}, "ProductWithPricing": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"title": "Description", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "company": {"anyOf": [{"$ref": "#/components/schemas/CompanyOut"}, {"type": "null"}]}, "category": {"anyOf": [{"$ref": "#/components/schemas/CategoryOut"}, {"type": "null"}]}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"title": "Unit Count", "type": "number"}, "base_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Base Price"}, "other_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Other Price"}}, "required": ["id", "name", "title", "barcode", "slug", "description", "unit", "unit_count"], "title": "ProductWithPricing", "type": "object"}, "CustomUserOut": {"description": "Schema for user output", "properties": {"id": {"title": "Id", "type": "integer"}, "username": {"title": "Username", "type": "string"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"title": "Phone", "type": "string"}, "phone_verified": {"title": "Phone Verified", "type": "boolean"}, "first_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Name"}, "is_active": {"title": "Is Active", "type": "boolean"}, "is_staff": {"title": "Is Staff", "type": "boolean"}, "is_superuser": {"title": "Is Superuser", "type": "boolean"}, "date_joined": {"format": "date-time", "title": "Date Joined", "type": "string"}, "last_login": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Last Login"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "username", "phone", "phone_verified", "is_active", "is_staff", "is_superuser", "date_joined", "created_at", "updated_at"], "title": "CustomUserOut", "type": "object"}, "PaginatedCustomUserResponse": {"description": "Paginated response for users", "properties": {"users": {"items": {"$ref": "#/components/schemas/CustomUserOut"}, "title": "Users", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["users", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedCustomUserResponse", "type": "object"}, "CustomUserIn": {"description": "Schema for creating a new user", "properties": {"username": {"title": "Username", "type": "string"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"title": "Phone", "type": "string"}, "password": {"title": "Password", "type": "string"}, "first_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Name"}}, "required": ["username", "phone", "password"], "title": "CustomUserIn", "type": "object"}, "CustomUserUpdate": {"description": "Schema for updating user data", "properties": {"username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Username"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "first_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Name"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "title": "CustomUserUpdate", "type": "object"}, "CategorySchema": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "slug": {"title": "Slug", "type": "string"}}, "required": ["id", "name", "title", "slug"], "title": "CategorySchema", "type": "object"}, "CompanySchema": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "slug": {"title": "Slug", "type": "string"}}, "required": ["id", "name", "title", "slug"], "title": "CompanySchema", "type": "object"}, "ItemProductSchema": {"properties": {"id": {"title": "Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image"}, "company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"title": "Unit Count", "type": "number"}}, "required": ["id", "title", "unit", "unit_count"], "title": "ItemProductSchema", "type": "object"}, "ItemSchema": {"properties": {"id": {"title": "Id", "type": "integer"}, "base_price": {"title": "Base Price", "type": "number"}, "inventory_count": {"title": "Inventory Count", "type": "integer"}, "minimum_order_quantity": {"title": "Minimum Order Quantity", "type": "integer"}, "maximum_order_quantity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Maximum Order Quantity"}, "product": {"anyOf": [{"$ref": "#/components/schemas/ItemProductSchema"}, {"type": "null"}]}, "wholesaler_id": {"title": "Wholesaler Id", "type": "integer"}, "wholesaler": {"anyOf": [{"$ref": "#/components/schemas/WholesalerSchema"}, {"type": "null"}]}}, "required": ["id", "base_price", "inventory_count", "minimum_order_quantity", "maximum_order_quantity", "wholesaler_id"], "title": "ItemSchema", "type": "object"}, "ProductSchema": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"title": "Description", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "company": {"anyOf": [{"$ref": "#/components/schemas/CompanySchema"}, {"type": "null"}]}, "category": {"anyOf": [{"$ref": "#/components/schemas/CategorySchema"}, {"type": "null"}]}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"title": "Unit Count", "type": "number"}, "items": {"anyOf": [{"items": {"$ref": "#/components/schemas/ItemSchema"}, "type": "array"}, {"type": "null"}], "title": "Items"}}, "required": ["id", "name", "title", "barcode", "slug", "description", "unit", "unit_count"], "title": "ProductSchema", "type": "object"}, "WholesalerSchema": {"properties": {"id": {"title": "Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "username": {"title": "Username", "type": "string"}, "background_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Background Image"}, "logo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Logo"}}, "required": ["id", "title", "username", "background_image", "logo"], "title": "WholesalerSchema", "type": "object"}, "PaginatedResponse_RegionSchema_": {"properties": {"data": {"items": {"$ref": "#/components/schemas/RegionSchema"}, "title": "Data", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["data", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedResponse[RegionSchema]", "type": "object"}, "RegionSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "ID"}, "name": {"description": "Region name, e.g. USA, California, San Francisco", "maxLength": 255, "title": "Name", "type": "string"}, "type": {"default": "OTHER", "description": "Type of region", "maxLength": 20, "title": "Type", "type": "string"}, "parent": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Parent region. E.g. USA is parent of California, California is parent of San Francisco", "title": "Parent"}, "code": {"anyOf": [{"maxLength": 50, "type": "string"}, {"type": "null"}], "description": "Optional region code (e.g. ISO country code, state abbreviation)", "title": "Code"}, "slug": {"title": "Slug", "type": "string"}, "is_active": {"default": true, "title": "Is Active", "type": "boolean"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "deleted_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Deleted At"}}, "required": ["name", "slug", "created_at", "updated_at"], "title": "RegionSchema", "type": "object"}, "PaginatedResponse_StoreSchema_": {"properties": {"data": {"items": {"$ref": "#/components/schemas/StoreSchema"}, "title": "Data", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["data", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedResponse[StoreSchema]", "type": "object"}, "StoreSchema": {"properties": {"id": {"title": "Id", "type": "integer"}, "owner_id": {"title": "Owner Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "address": {"title": "Address", "type": "string"}, "city_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "City Id"}, "state_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "State Id"}, "country_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Country Id"}}, "required": ["id", "owner_id", "name", "description", "address"], "title": "StoreSchema", "type": "object"}, "StoreIn": {"description": "Schema for creating a new store", "properties": {"name": {"title": "Name", "type": "string"}, "description": {"default": "", "title": "Description", "type": "string"}, "address": {"title": "Address", "type": "string"}, "city_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "City Id"}, "state_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "State Id"}, "country_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Country Id"}, "owner_id": {"title": "Owner Id", "type": "integer"}}, "required": ["name", "address", "owner_id"], "title": "StoreIn", "type": "object"}, "StoreUpdate": {"description": "Schema for updating store data", "properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address"}, "city_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "City Id"}, "state_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "State Id"}, "country_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Country Id"}}, "title": "StoreUpdate", "type": "object"}, "OrderItemOut2": {"properties": {"id": {"title": "Id", "type": "integer"}, "order_id": {"title": "Order Id", "type": "integer"}, "product_item_id": {"title": "Product Item Id", "type": "integer"}, "quantity": {"title": "Quantity", "type": "integer"}, "price_per_unit": {"title": "Price Per Unit", "type": "number"}, "total_price": {"title": "Total Price", "type": "number"}, "product": {"$ref": "#/components/schemas/ProductbOut"}}, "required": ["id", "order_id", "product_item_id", "quantity", "price_per_unit", "total_price", "product"], "title": "OrderItemOut2", "type": "object"}, "OrderOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "wholesaler": {"$ref": "#/components/schemas/WholesalerOut"}, "store": {"$ref": "#/components/schemas/StoreOrderOut"}, "order_items": {"items": {"$ref": "#/components/schemas/OrderItemOut2"}, "title": "Order Items", "type": "array"}, "total_price": {"title": "Total Price", "type": "number"}, "fees": {"title": "Fees", "type": "number"}, "products_total_price": {"title": "Products Total Price", "type": "number"}, "products_total_quantity": {"title": "Products Total Quantity", "type": "integer"}, "status": {"title": "Status", "type": "string"}, "status_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status Reason"}, "final_completed_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Final Completed Price"}, "deliver_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deliver At"}, "completed_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Completed At"}, "created_at": {"title": "Created At", "type": "string"}}, "required": ["id", "wholesaler", "store", "order_items", "total_price", "fees", "products_total_price", "products_total_quantity", "status", "created_at"], "title": "OrderOut", "type": "object"}, "PaginatedOrderResponse": {"description": "Paginated response for orders", "properties": {"orders": {"items": {"$ref": "#/components/schemas/OrderOut"}, "title": "Orders", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["orders", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedOrderResponse", "type": "object"}, "ProductbOut": {"description": "Basic product info for order items", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"title": "Unit Count", "type": "number"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}}, "required": ["id", "name", "title", "unit", "unit_count"], "title": "ProductbOut", "type": "object"}, "StoreOrderOut": {"description": "Simplified store schema for order responses", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "address": {"title": "Address", "type": "string"}}, "required": ["id", "name", "address"], "title": "StoreOrderOut", "type": "object"}, "WholesalerOut": {"description": "Simplified wholesaler schema for order responses", "properties": {"id": {"title": "Id", "type": "integer"}, "logo": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Logo"}, "title": {"title": "Title", "type": "string"}}, "required": ["id", "title"], "title": "WholesalerOut", "type": "object"}, "OrderIn": {"description": "Schema for creating a new order", "properties": {"wholesaler_id": {"title": "Wholesaler Id", "type": "integer"}, "store_id": {"title": "Store Id", "type": "integer"}, "items": {"items": {"$ref": "#/components/schemas/OrderItemIn"}, "title": "Items", "type": "array"}, "deliver_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deliver At"}}, "required": ["wholesaler_id", "store_id", "items"], "title": "OrderIn", "type": "object"}, "OrderItemIn": {"description": "Schema for order items input", "properties": {"item_id": {"title": "Item Id", "type": "integer"}, "quantity": {"title": "Quantity", "type": "integer"}}, "required": ["item_id", "quantity"], "title": "OrderItemIn", "type": "object"}, "PaginatedResponse_WholesalerSchema_": {"properties": {"data": {"items": {"$ref": "#/components/schemas/WholesalerSchema"}, "title": "Data", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["data", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedResponse[WholesalerSchema]", "type": "object"}, "RegionMinChargeSchema": {"properties": {"wholesaler_id": {"title": "Wholesaler Id", "type": "integer"}, "region_id": {"title": "Region Id", "type": "integer"}, "min_charge": {"title": "Min Charge", "type": "number"}, "min_items": {"title": "Min Items", "type": "integer"}}, "required": ["wholesaler_id", "region_id", "min_charge", "min_items"], "title": "RegionMinChargeSchema", "type": "object"}, "PaginatedResponse_ItemSchema_": {"properties": {"data": {"items": {"$ref": "#/components/schemas/ItemSchema"}, "title": "Data", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["data", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedResponse[ItemSchema]", "type": "object"}, "DailySalesOut": {"description": "Daily sales data schema", "properties": {"date": {"title": "Date", "type": "string"}, "sales": {"title": "Sales", "type": "number"}}, "required": ["date", "sales"], "title": "DailySalesOut", "type": "object"}, "DashboardOut": {"description": "Complete dashboard response schema", "properties": {"stats": {"$ref": "#/components/schemas/DashboardStatsOut"}, "recent_orders": {"items": {"$ref": "#/components/schemas/RecentOrderOut"}, "title": "Recent Orders", "type": "array"}, "daily_sales": {"items": {"$ref": "#/components/schemas/DailySalesOut"}, "title": "Daily Sales", "type": "array"}}, "required": ["stats", "recent_orders", "daily_sales"], "title": "DashboardOut", "type": "object"}, "DashboardStatsOut": {"description": "Dashboard statistics schema", "properties": {"total_orders": {"title": "Total Orders", "type": "integer"}, "pending_orders": {"title": "Pending Orders", "type": "integer"}, "total_items": {"title": "Total Items", "type": "integer"}, "low_stock_items": {"title": "Low Stock Items", "type": "integer"}}, "required": ["total_orders", "pending_orders", "total_items", "low_stock_items"], "title": "DashboardStatsOut", "type": "object"}, "RecentOrderOut": {"description": "Recent order schema for dashboard", "properties": {"id": {"title": "Id", "type": "integer"}, "store": {"$ref": "#/components/schemas/StoreOut"}, "status": {"title": "Status", "type": "string"}, "total_price": {"title": "Total Price", "type": "number"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}}, "required": ["id", "store", "status", "total_price", "created_at"], "title": "RecentOrderOut", "type": "object"}, "StoreOut": {"description": "Store information schema", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "address": {"title": "Address", "type": "string"}, "owner_username": {"title": "Owner <PERSON><PERSON><PERSON>", "type": "string"}, "owner_phone": {"title": "Owner Phone", "type": "string"}}, "required": ["id", "name", "address", "owner_username", "owner_phone"], "title": "StoreOut", "type": "object"}, "OrderDetailOut": {"description": "Detailed order information schema", "properties": {"id": {"title": "Id", "type": "integer"}, "store": {"$ref": "#/components/schemas/StoreOut"}, "status": {"title": "Status", "type": "string"}, "total_price": {"title": "Total Price", "type": "number"}, "fees": {"title": "Fees", "type": "number"}, "products_total_price": {"title": "Products Total Price", "type": "number"}, "products_total_quantity": {"title": "Products Total Quantity", "type": "integer"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "order_items": {"items": {"$ref": "#/components/schemas/OrderItemOut"}, "title": "Order Items", "type": "array"}}, "required": ["id", "store", "status", "total_price", "fees", "products_total_price", "products_total_quantity", "created_at", "updated_at", "order_items"], "title": "OrderDetailOut", "type": "object"}, "OrderItemOut": {"description": "Order item schema", "properties": {"id": {"title": "Id", "type": "integer"}, "product": {"$ref": "#/components/schemas/ProductItemOut"}, "quantity": {"title": "Quantity", "type": "integer"}, "price_per_unit": {"title": "Price Per Unit", "type": "number"}, "total_price": {"title": "Total Price", "type": "number"}}, "required": ["id", "product", "quantity", "price_per_unit", "total_price"], "title": "OrderItemOut", "type": "object"}, "OrderStatsOut": {"description": "Order statistics schema", "properties": {"total_orders": {"title": "Total Orders", "type": "integer"}, "pending_orders": {"title": "Pending Orders", "type": "integer"}, "completed_orders": {"title": "Completed Orders", "type": "integer"}}, "required": ["total_orders", "pending_orders", "completed_orders"], "title": "OrderStatsOut", "type": "object"}, "PaginatedOrdersOut": {"description": "Paginated orders response schema", "properties": {"orders": {"items": {"$ref": "#/components/schemas/OrderDetailOut"}, "title": "Orders", "type": "array"}, "stats": {"$ref": "#/components/schemas/OrderStatsOut"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["orders", "stats", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedOrdersOut", "type": "object"}, "ProductItemOut": {"description": "Product information for order items", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "company_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Company Name"}, "category_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category Name"}}, "required": ["id", "name", "title", "barcode"], "title": "ProductItemOut", "type": "object"}, "UpdateOrderStatusIn": {"description": "Update order status request schema", "properties": {"status": {"title": "Status", "type": "string"}}, "required": ["status"], "title": "UpdateOrderStatusIn", "type": "object"}, "RemoveOrderItemIn": {"description": "Remove order item request schema", "properties": {"reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "المنتج غير متوفر في المخزون", "title": "Reason"}}, "title": "RemoveOrderItemIn", "type": "object"}, "DecreaseOrderItemQuantityIn": {"description": "Decrease order item quantity request schema", "properties": {"new_quantity": {"title": "New Quantity", "type": "integer"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "تقليل الكمية - توفر جزئي", "title": "Reason"}}, "required": ["new_quantity"], "title": "DecreaseOrderItemQuantityIn", "type": "object"}, "ItemOut": {"description": "Item information schema", "properties": {"id": {"title": "Id", "type": "integer"}, "product": {"$ref": "#/components/schemas/ItemProductOut"}, "base_price": {"title": "Base Price", "type": "number"}, "inventory_count": {"title": "Inventory Count", "type": "integer"}, "minimum_order_quantity": {"title": "Minimum Order Quantity", "type": "integer"}, "maximum_order_quantity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Maximum Order Quantity"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "product", "base_price", "inventory_count", "minimum_order_quantity", "created_at", "updated_at"], "title": "ItemOut", "type": "object"}, "ItemProductOut": {"description": "Product information for items", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "company": {"anyOf": [{"$ref": "#/components/schemas/CompanyOut"}, {"type": "null"}]}, "category": {"anyOf": [{"$ref": "#/components/schemas/CategoryOut"}, {"type": "null"}]}}, "required": ["id", "name", "title", "barcode"], "title": "ItemProductOut", "type": "object"}, "ItemStatsOut": {"description": "Item statistics schema", "properties": {"total_items": {"title": "Total Items", "type": "integer"}, "low_stock_items": {"title": "Low Stock Items", "type": "integer"}, "out_of_stock_items": {"title": "Out Of Stock Items", "type": "integer"}}, "required": ["total_items", "low_stock_items", "out_of_stock_items"], "title": "ItemStatsOut", "type": "object"}, "PaginatedItemsOut": {"description": "Paginated items response schema", "properties": {"items": {"items": {"$ref": "#/components/schemas/ItemOut"}, "title": "Items", "type": "array"}, "stats": {"$ref": "#/components/schemas/ItemStatsOut"}, "companies": {"items": {"$ref": "#/components/schemas/CompanyOut"}, "title": "Companies", "type": "array"}, "categories": {"items": {"$ref": "#/components/schemas/CategoryOut"}, "title": "Categories", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["items", "stats", "companies", "categories", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedItemsOut", "type": "object"}, "ItemUpdateIn": {"description": "Update item request schema", "properties": {"base_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Base Price"}, "inventory_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Inventory Count"}, "minimum_order_quantity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Minimum Order Quantity"}, "maximum_order_quantity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Maximum Order Quantity"}}, "title": "ItemUpdateIn", "type": "object"}, "PaginatedProductsForBulkOut": {"description": "Paginated products response for bulk operations", "properties": {"products": {"items": {"$ref": "#/components/schemas/ProductForBulkOut"}, "title": "Products", "type": "array"}, "companies": {"items": {"$ref": "#/components/schemas/CompanyOut"}, "title": "Companies", "type": "array"}, "categories": {"items": {"$ref": "#/components/schemas/CategoryOut"}, "title": "Categories", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["products", "companies", "categories", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedProductsForBulkOut", "type": "object"}, "ProductForBulkOut": {"description": "Product information for bulk operations", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "company": {"anyOf": [{"$ref": "#/components/schemas/CompanyOut"}, {"type": "null"}]}, "category": {"anyOf": [{"$ref": "#/components/schemas/CategoryOut"}, {"type": "null"}]}}, "required": ["id", "name", "title", "barcode"], "title": "ProductForBulkOut", "type": "object"}, "BulkAddResultOut": {"description": "Bulk add operation result", "properties": {"created_items": {"items": {"$ref": "#/components/schemas/ItemOut"}, "title": "Created Items", "type": "array"}, "failed_items": {"items": {"additionalProperties": true, "type": "object"}, "title": "Failed Items", "type": "array"}, "success_count": {"title": "Success Count", "type": "integer"}, "failure_count": {"title": "Failure Count", "type": "integer"}}, "required": ["created_items", "failed_items", "success_count", "failure_count"], "title": "BulkAddResultOut", "type": "object"}, "BulkAddItemsIn": {"description": "Bulk add items request schema", "properties": {"product_ids": {"items": {"type": "integer"}, "title": "Product Ids", "type": "array"}}, "required": ["product_ids"], "title": "BulkAddItemsIn", "type": "object"}, "BulkUpdateResultOut": {"description": "Bulk update operation result", "properties": {"updated_items": {"items": {"$ref": "#/components/schemas/ItemOut"}, "title": "Updated Items", "type": "array"}, "success_count": {"title": "Success Count", "type": "integer"}, "failure_count": {"title": "Failure Count", "type": "integer"}}, "required": ["updated_items", "success_count", "failure_count"], "title": "BulkUpdateResultOut", "type": "object"}, "BulkUpdateItemsIn": {"description": "Bulk update items request schema", "properties": {"item_ids": {"items": {"type": "integer"}, "title": "Item <PERSON>", "type": "array"}, "base_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Base Price"}, "inventory_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Inventory Count"}, "minimum_order_quantity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Minimum Order Quantity"}, "maximum_order_quantity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Maximum Order Quantity"}}, "required": ["item_ids"], "title": "BulkUpdateItemsIn", "type": "object"}, "Banner": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "ID"}, "title": {"maxLength": 255, "title": "Title", "type": "string"}, "image": {"title": "Image", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "expire_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Expire Date"}, "is_active": {"default": true, "title": "Is Active", "type": "boolean"}, "regions": {"items": {"type": "integer"}, "title": "Regions", "type": "array"}}, "required": ["title", "image", "created_at", "updated_at", "regions"], "title": "Banner", "type": "object"}}, "securitySchemes": {"AuthBearerMiddleware": {"type": "http", "scheme": "bearer"}}}, "servers": []}